import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, FileText, TrendingUp, Users, CheckCircle, Building, Star, Crown, Edit, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const ContentMarketingPune = () => {
  const contentServices = [
    'Content Marketing Strategy Pune',
    'Content Creation Services Pune',
    'Blog Writing Services Pune',
    'Copywriting Services Pune',
    'Technical Writing Pune',
    'Website Content Writing Pune',
    'SEO Content Writing Pune',
    'Social Media Content Pune',
    'Email Content Creation Pune',
    'Video Content Scripts Pune',
    'Infographic Content Pune',
    'White Paper Writing Pune',
    'Case Study Writing Pune',
    'Press Release Writing Pune',
    'Product Description Writing Pune',
    'Educational Content Pune',
    'IT Content Writing Pune',
    'Manufacturing Content Pune',
    'Startup Content Marketing Pune',
    'B2B Content Strategy Pune'
  ];

  const contentTypes = [
    {
      type: 'Educational Content Marketing',
      description: 'Academic and educational content for Pune\'s educational institutions and coaching centers',
      icon: Building,
      features: ['Course Content', 'Educational Blogs', 'Academic Papers', 'Student Resources']
    },
    {
      type: 'IT & Tech Content',
      description: 'Technical content for Pune\'s IT corridor and software companies',
      icon: Edit,
      features: ['Technical Documentation', 'Software Blogs', 'Product Content', 'Developer Resources']
    },
    {
      type: 'Manufacturing Content',
      description: 'Industrial content for Pune\'s manufacturing and automotive sector',
      icon: Zap,
      features: ['Industrial Articles', 'Product Catalogs', 'Technical Specs', 'B2B Content']
    },
    {
      type: 'Startup Content Strategy',
      description: 'Growth-focused content for Pune\'s vibrant startup ecosystem',
      icon: Star,
      features: ['Startup Stories', 'Growth Content', 'Investor Pitch Content', 'Brand Narratives']
    }
  ];

  const contentPackages = [
    {
      name: 'Content Marketing Pune Starter',
      price: '₹22,000',
      period: '/month',
      description: 'Perfect for small Pune businesses and startups',
      features: [
        '8 Blog Posts per Month',
        'SEO-Optimized Content',
        'Social Media Content (20 posts)',
        'Basic Graphics & Images',
        'Content Calendar',
        'Educational Sector Focus'
      ]
    },
    {
      name: 'Pune Content Marketing Professional',
      price: '₹38,000',
      period: '/month',
      description: 'Comprehensive content marketing for growing Pune businesses',
      features: [
        '16 Blog Posts per Month',
        'Website Content Updates',
        'Social Media Content (40 posts)',
        'Email Newsletter Content',
        'Video Script Writing',
        'Advanced SEO Content',
        'IT Sector Specialization'
      ],
      popular: true
    },
    {
      name: 'Enterprise Content Marketing Pune',
      price: '₹68,000',
      period: '/month',
      description: 'Advanced content marketing for large Pune enterprises',
      features: [
        'Unlimited Content Creation',
        'Multi-format Content',
        'Video Content Production',
        'White Papers & Case Studies',
        'Thought Leadership Content',
        'Dedicated Content Manager',
        'Priority Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '1600+',
      description: 'Content Pieces Created',
      detail: 'For Pune businesses'
    },
    {
      metric: '340%',
      description: 'Average Traffic Increase',
      detail: 'Through content marketing'
    },
    {
      metric: '₹24Cr+',
      description: 'Revenue Generated',
      detail: 'Through content-driven leads'
    },
    {
      metric: '85%',
      description: 'Lead Quality Improvement',
      detail: 'With targeted content'
    }
  ];

  const achievements = [
    'Top Content Marketing Agency in Pune',
    'Educational Content Specialists',
    'IT Content Experts',
    'Manufacturing Content Leaders',
    'Startup Content Champions',
    'Technical Writing Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-indigo-500/20 border border-indigo-500/30 rounded-full px-6 py-2 mb-8">
                    <FileText className="w-4 h-4 text-indigo-400" />
                    <span className="text-indigo-400 font-medium">Content Marketing Pune • Oxford of the East Content Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Content Marketing Company in
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Pune</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier content marketing in Pune offering comprehensive content creation and strategy services for educational institutions, IT companies, and manufacturing businesses. Serving 1600+ Pune businesses across all areas - from educational hubs in Kothrud to IT corridors in Hinjewadi. Expert content marketing solutions with proven ₹24Cr+ revenue generation and 340% average traffic increase for Pune clients in Maharashtra's Oxford of the East.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Pune Content Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-indigo-500 text-indigo-400 hover:bg-indigo-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Content Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-indigo-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Pune Content Marketing
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable content marketing results from Pune businesses across educational, IT, and manufacturing sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-indigo-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Content Types Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Content Types We
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Create</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized content creation services designed for Pune's unique educational, IT, and industrial landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {contentTypes.map((content, index) => (
                    <Card key={index} className="bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mb-6">
                          <content.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{content.type}</h3>
                        <p className="text-slate-300 mb-6">{content.description}</p>
                        <ul className="space-y-2">
                          {content.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-indigo-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Pune Content Marketing
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive content marketing pricing designed for Pune's educational and IT business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {contentPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-indigo-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-indigo-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-indigo-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-indigo-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-indigo-600 to-indigo-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Pune with Content?</h2>
                <p className="text-xl mb-8">
                  Join 1600+ Pune businesses that trust GOD Digital Marketing for content marketing success. Proven strategies that deliver 340% traffic increase and ₹24Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-indigo-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Content Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-indigo-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Content Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="content" currentLocation="pune" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default ContentMarketingPune;
