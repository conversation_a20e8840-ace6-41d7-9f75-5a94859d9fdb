
import React from 'react';
import { Link } from 'react-router-dom';
import { Mail, Phone, MapPin, Facebook, Twitter, Linkedin, Instagram, Globe } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-slate-900 border-t border-amber-500/20 relative z-10">
      <div className="container mx-auto px-6 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Column 1 - Core Services */}
          <div>
            <h3 className="text-white font-semibold mb-6">Core Services</h3>
            <ul className="space-y-3">
              <li><Link to="/services/seo" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">SEO Services India</Link></li>
              <li><Link to="/services/seo/local" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Local SEO Solutions</Link></li>
              <li><Link to="/services/seo/international" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">International SEO</Link></li>
              <li><Link to="/services/ppc" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Google Ads Management</Link></li>
              <li><Link to="/services/social-media" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Social Media Marketing</Link></li>
              <li><Link to="/services/content" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Content Marketing</Link></li>
              <li><Link to="/services/email" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Email Marketing</Link></li>
              <li><Link to="/services/web-development" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Web Development</Link></li>
              <li><Link to="/services/ai-content" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">AI Content Creation</Link></li>
              <li><Link to="/services/marketing-automation" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Marketing Automation</Link></li>
            </ul>
          </div>

          {/* Column 2 - Service-Location Combinations */}
          <div>
            <h3 className="text-white font-semibold mb-6">Service-Location Combinations</h3>
            <ul className="space-y-3">
              <li><Link to="/services/seo/delhi" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">SEO Services Delhi</Link></li>
              <li><Link to="/services/seo/mumbai" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">SEO Services Mumbai</Link></li>
              <li><Link to="/services/seo/bangalore" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">SEO Services Bangalore</Link></li>
              <li><Link to="/services/seo/chennai" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">SEO Services Chennai</Link></li>
              <li><Link to="/services/ppc/delhi" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Google Ads Delhi</Link></li>
              <li><Link to="/services/ppc/mumbai" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Google Ads Mumbai</Link></li>
              <li><Link to="/services/ppc/bangalore" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Google Ads Bangalore</Link></li>
              <li><Link to="/services/social-media/delhi" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Social Media Delhi</Link></li>
              <li><Link to="/services/content/mumbai" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Content Marketing Mumbai</Link></li>
              <li><Link to="/services/email/delhi" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Email Marketing Delhi</Link></li>
            </ul>
          </div>

          {/* Column 3 - Key Industries */}
          <div>
            <h3 className="text-white font-semibold mb-6">Key Industries</h3>
            <ul className="space-y-3">
              <li><Link to="/industries/real-estate" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Real Estate SEO</Link></li>
              <li><Link to="/industries/healthcare" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Healthcare Marketing</Link></li>
              <li><Link to="/industries/education" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Education Digital Solutions</Link></li>
              <li><Link to="/industries/ecommerce" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">E-commerce Growth</Link></li>
              <li><Link to="/industries/manufacturing" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Manufacturing SEO</Link></li>
              <li><Link to="/industries/financial-services" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Legal Services Marketing</Link></li>
            </ul>
          </div>

          {/* Column 4 - Resources */}
          <div>
            <h3 className="text-white font-semibold mb-6">Resources</h3>
            <ul className="space-y-3">
              <li><Link to="/blog" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">SEO Blog</Link></li>
              <li><Link to="/resources/digital-marketing-guide" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Digital Marketing Guide</Link></li>
              <li><Link to="/resources/ai-automation" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">AI Automation Resources</Link></li>
              <li><Link to="/case-studies" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Case Studies Library</Link></li>
              <li><Link to="/resources/industry-reports" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Industry Reports</Link></li>
              <li><Link to="/tools/seo-audit" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Free SEO Tools</Link></li>
            </ul>
          </div>

          {/* Column 5 - Company Info */}
          <div>
            <h3 className="text-white font-semibold mb-6">Company Info</h3>
            <ul className="space-y-3">
              <li><Link to="/about" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">About Us</Link></li>
              <li><Link to="/about#team" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Our Team</Link></li>
              <li><Link to="/about#experience" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">International Experience</Link></li>
              <li><Link to="/testimonials" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Client Testimonials</Link></li>
              <li><Link to="/awards" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Awards</Link></li>
              <li><Link to="/careers" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Career Opportunities</Link></li>
            </ul>
          </div>

          {/* Column 6 - Quick Links & Contact */}
          <div>
            <Link to="/" className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-r from-amber-400 to-amber-600 rounded-lg flex items-center justify-center font-bold text-slate-900 text-lg">
                G
              </div>
              <div>
                <div className="text-xl font-bold text-white">GOD</div>
                <div className="text-sm text-amber-400 -mt-1">Digital Marketing</div>
              </div>
            </Link>

            <h3 className="text-white font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-3 mb-6">
              <li><Link to="/quote" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Free SEO Audit</Link></li>
              <li><Link to="/quote" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Get Quote</Link></li>
              <li><Link to="/contact" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Schedule Consultation</Link></li>
              <li><Link to="/contact" className="text-slate-300 hover:text-amber-400 transition-colors text-sm">Contact Information</Link></li>
            </ul>

            <div className="space-y-3 mb-6">
              <div className="flex items-center space-x-2">
                <Globe className="w-4 h-4 text-amber-400" />
                <span className="text-slate-300 text-sm">6 Countries Experience</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="w-4 h-4 text-amber-400" />
                <span className="text-slate-300 text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="w-4 h-4 text-amber-400" />
                <span className="text-slate-300 text-sm">+91-8708577598</span>
              </div>
            </div>

            <div className="flex space-x-4">
              <a href="#" className="text-slate-400 hover:text-amber-400 transition-colors">
                <Facebook className="w-5 h-5" />
              </a>
              <a href="#" className="text-slate-400 hover:text-amber-400 transition-colors">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="#" className="text-slate-400 hover:text-amber-400 transition-colors">
                <Linkedin className="w-5 h-5" />
              </a>
              <a href="#" className="text-slate-400 hover:text-amber-400 transition-colors">
                <Instagram className="w-5 h-5" />
              </a>
            </div>
          </div>
        </div>

        <div className="border-t border-slate-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-slate-400 text-sm">
            © 2024 GOD Digital Marketing. All rights reserved. International SEO Specialist across UK, US, Dubai, India, Kuwait & South Africa.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link to="/privacy-policy" className="text-slate-400 hover:text-amber-400 text-sm transition-colors">Privacy Policy</Link>
            <Link to="/sitemap" className="text-slate-400 hover:text-amber-400 text-sm transition-colors">Sitemap</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
