import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Target, TrendingUp, Users, CheckCircle, Building, Star, Crown, DollarSign, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const GoogleAdsAhmedabad = () => {
  const googleAdsServices = [
    'Google Ads Management Ahmedabad',
    'PPC Campaign Management Ahmedabad',
    'Search Ads Ahmedabad',
    'Display Advertising Ahmedabad',
    'Shopping Ads Ahmedabad',
    'YouTube Ads Ahmedabad',
    'Local Ads Ahmedabad',
    'Mobile Ads Ahmedabad',
    'Remarketing Campaigns Ahmedabad',
    'Lead Generation Ads Ahmedabad',
    'E-commerce PPC Ahmedabad',
    'Textile Industry Ads Ahmedabad',
    'Chemical Company Ads Ahmedabad',
    'Diamond & Jewelry Ads Ahmedabad',
    'Manufacturing Ads Ahmedabad',
    'Export Business Ads Ahmedabad',
    'Pharmaceutical Ads Ahmedabad',
    'B2B Lead Generation Ahmedabad',
    'Industrial Ads Ahmedabad',
    'Conversion Optimization Ahmedabad'
  ];

  const adSpecializations = [
    {
      type: 'Textile & Garment Advertising',
      description: 'Targeted advertising for Ahmedabad\'s textile capital and garment exporters',
      icon: Building,
      features: ['Textile B2B Ads', 'Garment Export Campaigns', 'Fashion Industry PPC', 'Fabric Supplier Ads']
    },
    {
      type: 'Chemical & Pharmaceutical Ads',
      description: 'B2B advertising for Gujarat\'s chemical and pharmaceutical industry leaders',
      icon: Target,
      features: ['Chemical Industry PPC', 'Pharma Lead Generation', 'Industrial B2B Ads', 'Export Market Campaigns']
    },
    {
      type: 'Diamond & Jewelry Advertising',
      description: 'Premium advertising for Ahmedabad\'s diamond cutting and jewelry sector',
      icon: Star,
      features: ['Diamond Industry Ads', 'Jewelry Export PPC', 'Precious Metals Ads', 'Luxury Brand Campaigns']
    },
    {
      type: 'Manufacturing & Engineering Ads',
      description: 'Industrial advertising for Ahmedabad\'s engineering and manufacturing companies',
      icon: Zap,
      features: ['Engineering B2B Ads', 'Manufacturing PPC', 'Industrial Equipment Ads', 'Export Lead Generation']
    }
  ];

  const adPackages = [
    {
      name: 'Google Ads Ahmedabad Starter',
      price: '₹22,000',
      period: '/month',
      description: 'Perfect for small Ahmedabad businesses and textile traders',
      features: [
        'Ad Spend: ₹18,000/month',
        'Search + Display Campaigns',
        'Local Ahmedabad Targeting',
        'Landing Page Optimization',
        'Monthly Performance Reports',
        'Textile Industry Focus'
      ]
    },
    {
      name: 'Ahmedabad Google Ads Professional',
      price: '₹38,000',
      period: '/month',
      description: 'Comprehensive advertising for growing Ahmedabad businesses',
      features: [
        'Ad Spend: ₹60,000/month',
        'Multi-Campaign Strategy',
        'Advanced Targeting',
        'Conversion Tracking',
        'A/B Testing',
        'Chemical/Pharma Specialization',
        'Bi-weekly Optimization'
      ],
      popular: true
    },
    {
      name: 'Enterprise Google Ads Ahmedabad',
      price: '₹72,000',
      period: '/month',
      description: 'Advanced advertising for large Ahmedabad enterprises and exporters',
      features: [
        'Ad Spend: ₹1,20,000+/month',
        'Full-Funnel Campaigns',
        'Advanced Analytics',
        'Multi-platform Integration',
        'Export Market Targeting',
        'Dedicated Account Manager',
        'Weekly Strategy Calls'
      ]
    }
  ];

  const stats = [
    {
      metric: '2100+',
      description: 'Ahmedabad Ad Campaigns',
      detail: 'Successfully managed'
    },
    {
      metric: '580%',
      description: 'Average ROAS',
      detail: 'Return on ad spend'
    },
    {
      metric: '₹38Cr+',
      description: 'Revenue Generated',
      detail: 'Through Google Ads'
    },
    {
      metric: '91%',
      description: 'Lead Quality Score',
      detail: 'High-intent prospects'
    }
  ];

  const achievements = [
    'Top Google Ads Agency in Ahmedabad',
    'Textile Industry PPC Leaders',
    'Chemical Sector Ad Experts',
    'Diamond Industry Ad Specialists',
    'Manufacturing PPC Champions',
    'Export Business Ad Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-red-500/20 border border-red-500/30 rounded-full px-6 py-2 mb-8">
                    <Target className="w-4 h-4 text-red-400" />
                    <span className="text-red-400 font-medium">Google Ads Ahmedabad • Gujarat's Commercial Capital Advertising</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Google Ads Company in
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Ahmedabad</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier Google Ads management in Ahmedabad offering comprehensive PPC advertising for textile, chemical, diamond, and manufacturing businesses. Serving 2100+ Ahmedabad businesses across all areas - from textile hubs in Narol to chemical zones in Vatva. Expert Google Ads solutions with proven ₹38Cr+ revenue generation and 580% average ROAS for Ahmedabad clients in Gujarat's commercial capital.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Ahmedabad Ads Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call PPC Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-red-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Ahmedabad Google Ads
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable Google Ads results from Ahmedabad businesses across textile, chemical, diamond, and manufacturing sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-red-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Ad Specializations Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Ahmedabad Google Ads
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Specializations</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized advertising strategies designed for Ahmedabad's unique textile, chemical, diamond, and manufacturing landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {adSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-red-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Ahmedabad Google Ads
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive Google Ads pricing designed for Ahmedabad's textile, chemical, and manufacturing business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {adPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-red-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-red-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-red-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-red-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-red-600 to-red-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Ahmedabad with Google Ads?</h2>
                <p className="text-xl mb-8">
                  Join 2100+ Ahmedabad businesses that trust GOD Digital Marketing for Google Ads success. Proven strategies that deliver 580% ROAS and ₹38Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-red-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Ads Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-red-600 text-lg px-8 py-4">
                    <Link to="/contact">Call PPC Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="ppc" currentLocation="ahmedabad" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default GoogleAdsAhmedabad;
