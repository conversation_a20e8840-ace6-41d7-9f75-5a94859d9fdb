
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Search, Globe, Bot, TrendingUp, Users, Building } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const ServicesOverview = () => {
  const services = [
    {
      icon: Globe,
      title: 'International SEO',
      description: 'Multi-country SEO campaigns that dominate global markets. Proven expertise across UK, US, Dubai, India, Kuwait & South Africa.',
      link: '/services/seo',
      features: ['Multi-Country Ranking', 'International Keyword Research', 'Global Technical SEO', 'Cross-Border Link Building']
    },
    {
      icon: Bo<PERSON>,
      title: 'AI-Powered Content Creation',
      description: 'Scale content production with advanced AI automation while maintaining quality and relevance for each market.',
      link: '/services/ai-content',
      features: ['AI Content at Scale', 'Multi-Language Optimization', 'Automated Publishing', 'Quality Control Systems']
    },
    {
      icon: TrendingUp,
      title: 'Business Growth Automation',
      description: 'Complete digital ecosystem automation that scales your business operations and lead generation systems.',
      link: '/services/automation',
      features: ['Lead Generation Systems', 'Sales Funnel Automation', 'CRM Integration', 'Performance Analytics']
    },
    {
      icon: Users,
      title: 'Social Media Automation',
      description: 'AI-powered social media management across all platforms with content scheduling and engagement automation.',
      link: '/services/social-automation',
      features: ['Multi-Platform Management', 'Content Automation', 'Engagement Bots', 'Analytics & Reporting']
    },
    {
      icon: Building,
      title: 'Real Estate Lead Generation',
      description: 'Specialized lead generation systems for real estate companies targeting high-value property leads.',
      link: '/services/real-estate',
      features: ['Property Lead Funnels', 'Local SEO Dominance', 'Buyer Intent Targeting', 'CRM Integration']
    },
    {
      icon: Search,
      title: 'Enterprise SEO Consulting',
      description: 'Premium SEO consulting for businesses ready to invest ₹60K-150K monthly for international market dominance.',
      link: '/services/enterprise-seo',
      features: ['Strategic SEO Planning', 'International Expansion', 'Technical SEO Audits', 'Competitive Analysis']
    }
  ];

  return (
    <section className="py-20 bg-slate-800/50 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Premium International SEO &
            <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> AI Automation</span>
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-8">
            Specialized services for businesses ready to dominate international markets. From zero to millions of monthly views across 6 countries - proven strategies that deliver premium results.
          </p>
          <Button asChild variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white">
            <Link to="/services">Explore Premium Services</Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 group cursor-pointer">
              <CardContent className="p-8">
                <div className="mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                    <service.icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-3">{service.title}</h3>
                  <p className="text-slate-300 mb-6">{service.description}</p>
                </div>

                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-slate-400">
                      <div className="w-2 h-2 bg-amber-400 rounded-full mr-3"></div>
                      {feature}
                    </li>
                  ))}
                </ul>

                <Button asChild variant="ghost" className="text-amber-400 hover:text-white hover:bg-amber-500 p-0">
                  <Link to={service.link} className="flex items-center">
                    Learn More
                    <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesOverview;
