import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Code, TrendingUp, Users, CheckCircle, Building, Star, Crown, Smartphone, Globe } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const WebDevelopmentHyderabad = () => {
  const webServices = [
    'Web Development Services Hyderabad',
    'Custom Website Development Hyderabad',
    'E-commerce Development Hyderabad',
    'Mobile App Development Hyderabad',
    'React Development Hyderabad',
    'Node.js Development Hyderabad',
    'PHP Development Hyderabad',
    'WordPress Development Hyderabad',
    'Shopify Development Hyderabad',
    'API Development Hyderabad',
    'Database Development Hyderabad',
    'Cloud Solutions Hyderabad',
    'DevOps Services Hyderabad',
    'UI/UX Design Hyderabad',
    'Progressive Web Apps Hyderabad',
    'Website Maintenance Hyderabad',
    'IT Solutions Development Hyderabad',
    'SaaS Development Hyderabad',
    'Enterprise Web Solutions Hyderabad',
    'Startup Web Development Hyderabad'
  ];

  const developmentTypes = [
    {
      type: 'Enterprise Web Solutions',
      description: 'Scalable web applications for Hyderabad\'s large IT enterprises',
      icon: Building,
      features: ['Microservices Architecture', 'Cloud-Native Solutions', 'Enterprise Security', 'Scalable Infrastructure']
    },
    {
      type: 'Startup Web Development',
      description: 'Agile and cost-effective web solutions for Hyderabad\'s startup ecosystem',
      icon: Star,
      features: ['MVP Development', 'Rapid Prototyping', 'Scalable Architecture', 'Cost-Effective Solutions']
    },
    {
      type: 'SaaS Platform Development',
      description: 'Software-as-a-Service platforms for Hyderabad\'s tech companies',
      icon: Globe,
      features: ['Multi-tenant Architecture', 'Subscription Management', 'API Integration', 'Performance Optimization']
    },
    {
      type: 'Mobile-First Development',
      description: 'Mobile-optimized web applications for Hyderabad\'s mobile-first market',
      icon: Smartphone,
      features: ['Responsive Design', 'Progressive Web Apps', 'Mobile Optimization', 'Cross-Platform Solutions']
    }
  ];

  const webPackages = [
    {
      name: 'Web Development Hyderabad Starter',
      price: '₹45,000',
      period: '/project',
      description: 'Perfect for small Hyderabad businesses needing a professional website',
      features: [
        'Custom Website Design',
        'Responsive Development',
        'Content Management System',
        'SEO Optimization',
        'Contact Forms',
        '3 Months Support'
      ]
    },
    {
      name: 'Hyderabad Web Development Professional',
      price: '₹85,000',
      period: '/project',
      description: 'Comprehensive web development for growing Hyderabad businesses',
      features: [
        'Advanced Custom Development',
        'E-commerce Integration',
        'Database Development',
        'API Integration',
        'Performance Optimization',
        'Security Implementation',
        '6 Months Support'
      ],
      popular: true
    },
    {
      name: 'Enterprise Web Development Hyderabad',
      price: '₹1,50,000',
      period: '/project',
      description: 'Advanced web solutions for large Hyderabad enterprises',
      features: [
        'Enterprise Architecture',
        'Custom Application Development',
        'Cloud Integration',
        'Advanced Security',
        'Scalable Infrastructure',
        'DevOps Implementation',
        '12 Months Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '450+',
      description: 'Websites Developed',
      detail: 'For Hyderabad businesses'
    },
    {
      metric: '99.9%',
      description: 'Uptime Guarantee',
      detail: 'Reliable hosting solutions'
    },
    {
      metric: '₹28Cr+',
      description: 'Revenue Generated',
      detail: 'Through web solutions'
    },
    {
      metric: '95%',
      description: 'Client Satisfaction Rate',
      detail: 'Quality delivery'
    }
  ];

  const achievements = [
    'Top Web Development Company in Hyderabad',
    'IT Industry Development Specialists',
    'Startup Web Solution Experts',
    'Enterprise Development Leaders',
    'SaaS Platform Development Champions',
    'Mobile-First Development Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-emerald-500/20 border border-emerald-500/30 rounded-full px-6 py-2 mb-8">
                    <Code className="w-4 h-4 text-emerald-400" />
                    <span className="text-emerald-400 font-medium">Web Development Hyderabad • Cyberabad Tech Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Web Development Company in
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Hyderabad</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier web development in Hyderabad offering comprehensive website development and application solutions from custom web development to enterprise solutions. Serving 450+ Hyderabad businesses across all areas - from IT companies in Hitech City to pharmaceutical companies in Genome Valley. Expert web development solutions with proven ₹28Cr+ revenue generation and 99.9% uptime guarantee for Hyderabad clients in India's Cyberabad.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Hyderabad Web Development Quote</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Web Developers: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-emerald-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Hyderabad Web Development
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable web development results from Hyderabad businesses across all industries.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-emerald-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Development Types Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Web Solutions We
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Develop</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized web development solutions designed for Hyderabad's IT, pharmaceutical, and startup landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {developmentTypes.map((dev, index) => (
                    <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center mb-6">
                          <dev.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{dev.type}</h3>
                        <p className="text-slate-300 mb-6">{dev.description}</p>
                        <ul className="space-y-2">
                          {dev.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-emerald-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Hyderabad Web Development
                    <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive web development pricing designed for Hyderabad's IT and pharmaceutical business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {webPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-emerald-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-emerald-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-emerald-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-emerald-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-emerald-600 to-emerald-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Build Your Hyderabad Web Presence?</h2>
                <p className="text-xl mb-8">
                  Join 450+ Hyderabad businesses that trust GOD Digital Marketing for web development success. Proven solutions that deliver 99.9% uptime and ₹28Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-emerald-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Development Quote</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-emerald-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Web Developers: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="web-development" currentLocation="hyderabad" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default WebDevelopmentHyderabad;
