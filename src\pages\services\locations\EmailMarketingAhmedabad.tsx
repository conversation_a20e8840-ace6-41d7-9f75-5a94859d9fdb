import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Mail, TrendingUp, Users, CheckCircle, Building, Star, Crown, Send, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const EmailMarketingAhmedabad = () => {
  const emailServices = [
    'Email Marketing Strategy Ahmedabad',
    'Email Campaign Management Ahmedabad',
    'Email Automation Ahmedabad',
    'Newsletter Marketing Ahmedabad',
    'Drip Campaign Setup Ahmedabad',
    'Email Template Design Ahmedabad',
    'Email List Building Ahmedabad',
    'Email Segmentation Ahmedabad',
    'Personalized Email Marketing Ahmedabad',
    'Transactional Email Setup Ahmedabad',
    'Email A/B Testing Ahmedabad',
    'Email Analytics & Reporting Ahmedabad',
    'Email Deliverability Optimization Ahmedabad',
    'CRM Email Integration Ahmedabad',
    'Textile Industry Email Marketing Ahmedabad',
    'Chemical Company Email Campaigns Ahmedabad',
    'Diamond & Jewelry Email Marketing Ahmedabad',
    'Manufacturing Email Marketing Ahmedabad',
    'Export Business Email Marketing Ahmedabad',
    'B2B Email Marketing Ahmedabad'
  ];

  const emailTypes = [
    {
      type: 'Textile & Garment Email Campaigns',
      description: 'Industry-specific email marketing for Ahmedabad\'s textile capital and garment exporters',
      icon: Building,
      features: ['B2B Textile Communications', 'Fashion Industry Updates', 'Export Market Newsletters', 'Trade Show Invitations']
    },
    {
      type: 'Chemical & Pharmaceutical Email Marketing',
      description: 'Professional email campaigns for Gujarat\'s chemical and pharmaceutical industry leaders',
      icon: Mail,
      features: ['Industry Compliance Updates', 'Technical Newsletters', 'B2B Lead Nurturing', 'Product Launch Campaigns']
    },
    {
      type: 'Diamond & Jewelry Email Campaigns',
      description: 'Premium email marketing for Ahmedabad\'s diamond cutting and jewelry manufacturing sector',
      icon: Star,
      features: ['Luxury Brand Communications', 'High-Value Client Nurturing', 'Premium Product Showcases', 'VIP Customer Programs']
    },
    {
      type: 'Manufacturing & Engineering Email Marketing',
      description: 'Industrial email campaigns for Ahmedabad\'s engineering and manufacturing companies',
      icon: Send,
      features: ['Industrial Updates', 'B2B Communications', 'Technical Product Info', 'Export Market Outreach']
    }
  ];

  const emailPackages = [
    {
      name: 'Email Marketing Ahmedabad Starter',
      price: '₹18,000',
      period: '/month',
      description: 'Perfect for small Ahmedabad businesses and textile traders',
      features: [
        'Up to 8,000 Subscribers',
        '6 Email Campaigns per Month',
        'Basic Email Templates',
        'List Management',
        'Basic Analytics',
        'Textile Industry Focus'
      ]
    },
    {
      name: 'Ahmedabad Email Marketing Professional',
      price: '₹32,000',
      period: '/month',
      description: 'Comprehensive email marketing for growing Ahmedabad businesses',
      features: [
        'Up to 30,000 Subscribers',
        'Unlimited Email Campaigns',
        'Custom Email Templates',
        'Advanced Automation',
        'A/B Testing',
        'Advanced Analytics',
        'Chemical/Pharma Specialization'
      ],
      popular: true
    },
    {
      name: 'Enterprise Email Marketing Ahmedabad',
      price: '₹58,000',
      period: '/month',
      description: 'Advanced email marketing for large Ahmedabad enterprises and exporters',
      features: [
        'Unlimited Subscribers',
        'Advanced Automation Workflows',
        'Custom Integrations',
        'Dedicated IP',
        'Advanced Segmentation',
        'Export Market Campaigns',
        'Dedicated Account Manager'
      ]
    }
  ];

  const stats = [
    {
      metric: '900K+',
      description: 'Emails Sent Monthly',
      detail: 'For Ahmedabad businesses'
    },
    {
      metric: '31%',
      description: 'Average Open Rate',
      detail: 'Above industry standard'
    },
    {
      metric: '₹19Cr+',
      description: 'Revenue Generated',
      detail: 'Through email campaigns'
    },
    {
      metric: '96%',
      description: 'Deliverability Rate',
      detail: 'Consistent inbox placement'
    }
  ];

  const achievements = [
    'Top Email Marketing Agency in Ahmedabad',
    'Textile Industry Email Leaders',
    'Chemical Sector Email Experts',
    'Diamond Industry Email Specialists',
    'Manufacturing Email Champions',
    'Export Business Email Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-sky-500/20 border border-sky-500/30 rounded-full px-6 py-2 mb-8">
                    <Mail className="w-4 h-4 text-sky-400" />
                    <span className="text-sky-400 font-medium">Email Marketing Ahmedabad • Gujarat's Commercial Capital Direct Communication</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Email Marketing Company in
                    <span className="bg-gradient-to-r from-sky-400 to-sky-600 bg-clip-text text-transparent"> Ahmedabad</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier email marketing in Ahmedabad offering comprehensive email campaign management and automation services for textile, chemical, diamond, and manufacturing businesses. Serving 900+ Ahmedabad businesses across all areas - from textile hubs in Narol to chemical zones in Vatva. Expert email marketing solutions with proven ₹19Cr+ revenue generation and 900K+ emails sent monthly for Ahmedabad clients in Gujarat's commercial capital.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Ahmedabad Email Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-sky-500 text-sky-400 hover:bg-sky-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Email Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-sky-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Ahmedabad Email Marketing
                    <span className="bg-gradient-to-r from-sky-400 to-sky-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable email marketing results from Ahmedabad businesses across textile, chemical, diamond, and manufacturing sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-sky-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Email Types Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Email Campaign Types We
                    <span className="bg-gradient-to-r from-sky-400 to-sky-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized email marketing campaigns designed for Ahmedabad's unique textile, chemical, diamond, and manufacturing landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {emailTypes.map((email, index) => (
                    <Card key={index} className="bg-slate-900/80 border-sky-500/20 hover:border-sky-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-sky-500 to-sky-600 rounded-lg flex items-center justify-center mb-6">
                          <email.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{email.type}</h3>
                        <p className="text-slate-300 mb-6">{email.description}</p>
                        <ul className="space-y-2">
                          {email.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-sky-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Ahmedabad Email Marketing
                    <span className="bg-gradient-to-r from-sky-400 to-sky-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive email marketing pricing designed for Ahmedabad's textile, chemical, and manufacturing business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {emailPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-sky-500/20 hover:border-sky-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-sky-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-sky-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-sky-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-sky-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-sky-600 to-sky-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Ahmedabad Inboxes?</h2>
                <p className="text-xl mb-8">
                  Join 900+ Ahmedabad businesses that trust GOD Digital Marketing for email marketing success. Proven strategies that deliver 31% open rates and ₹19Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-sky-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Email Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-sky-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Email Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="email" currentLocation="ahmedabad" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default EmailMarketingAhmedabad;
