
import React, { useRef, useMemo, Suspense } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Text, Float } from '@react-three/drei';
import { Mesh } from 'three';

// Optimized Indian Currency Symbol Component
const IndianCurrency = ({ position, delay = 0 }: { position: [number, number, number], delay?: number }) => {
  const meshRef = useRef<Mesh>(null);

  useFrame(() => {
    if (meshRef.current) {
      // Reduced calculations for better performance
      meshRef.current.position.y += 0.005;
      meshRef.current.rotation.y += 0.003;
      
      if (meshRef.current.position.y > 2) {
        meshRef.current.position.y = -2;
      }
    }
  });

  return (
    <Float speed={0.8} rotationIntensity={0.1} floatIntensity={0.05}>
      <mesh ref={meshRef} position={position}>
        <boxGeometry args={[0.08, 0.12, 0.02]} />
        <meshStandardMaterial 
          color="#fbbf24" 
          metalness={0.5}
          roughness={0.4}
        />
      </mesh>
    </Float>
  );
};

// Simplified Growth Chart Component
const GrowthChart = ({ position }: { position: [number, number, number] }) => {
  const groupRef = useRef<any>(null);

  useFrame(() => {
    if (groupRef.current) {
      groupRef.current.rotation.y += 0.001;
    }
  });

  return (
    <group ref={groupRef} position={position}>
      <mesh position={[-0.1, -0.06, 0]}>
        <boxGeometry args={[0.03, 0.06, 0.03]} />
        <meshStandardMaterial color="#10b981" />
      </mesh>
      <mesh position={[0, -0.03, 0]}>
        <boxGeometry args={[0.03, 0.12, 0.03]} />
        <meshStandardMaterial color="#10b981" />
      </mesh>
      <mesh position={[0.1, 0, 0]}>
        <boxGeometry args={[0.03, 0.18, 0.03]} />
        <meshStandardMaterial color="#10b981" />
      </mesh>
    </group>
  );
};

// Optimized Logo Scene
const LogoScene = () => {
  return (
    <>
      {/* Simplified lighting for better performance */}
      <ambientLight intensity={0.4} />
      <pointLight position={[2, 2, 2]} intensity={0.6} />
      
      {/* Main Logo Text */}
      <Float speed={1} rotationIntensity={0.02} floatIntensity={0.05}>
        <Text
          fontSize={0.5}
          color="#FFFFFF"
          anchorX="center"
          anchorY="middle"
          position={[0, 0.1, 0]}
        >
          GOD
        </Text>
        <Text
          fontSize={0.15}
          color="#fbbf24"
          anchorX="center"
          anchorY="middle"
          position={[0, -0.15, 0]}
        >
          Digital Marketing
        </Text>
      </Float>

      {/* Reduced number of currency symbols for performance */}
      <IndianCurrency position={[-0.8, -1, 0.2]} delay={0} />
      <IndianCurrency position={[0.8, -0.5, -0.2]} delay={1} />
      
      {/* Single growth chart */}
      <GrowthChart position={[-1, -0.2, -0.5]} />
    </>
  );
};

// Fallback component for loading
const LogoFallback = () => (
  <div className="w-full h-40 flex items-center justify-center bg-slate-900">
    <div className="text-center">
      <div className="text-3xl font-bold text-white mb-2">GOD</div>
      <div className="text-sm text-amber-400">Digital Marketing</div>
    </div>
  </div>
);

// Optimized Main 3D Logo Component
const Logo3D = ({ className = "" }: { className?: string }) => {
  const canvasSettings = useMemo(() => ({
    camera: { position: [0, 0, 2.5] as [number, number, number], fov: 45 },
    gl: { 
      antialias: false, // Disabled for better mobile performance
      alpha: true,
      powerPreference: "high-performance" as const,
      stencil: false,
      depth: false
    },
    dpr: Math.min(window.devicePixelRatio, 2), // Limit DPR for performance
    performance: { min: 0.5 }
  }), []);

  return (
    <div className={`w-full h-40 ${className}`}>
      <Suspense fallback={<LogoFallback />}>
        <Canvas {...canvasSettings}>
          <LogoScene />
        </Canvas>
      </Suspense>
    </div>
  );
};

export default Logo3D;
