import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Target, TrendingUp, Users, CheckCircle, Building, Star, Crown, DollarSign, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const GoogleAdsPune = () => {
  const googleAdsServices = [
    'Google Ads Management Pune',
    'PPC Campaign Management Pune',
    'Search Ads Pune',
    'Display Advertising Pune',
    'Shopping Ads Pune',
    'YouTube Ads Pune',
    'Local Ads Pune',
    'Mobile Ads Pune',
    'Remarketing Campaigns Pune',
    'Lead Generation Ads Pune',
    'E-commerce PPC Pune',
    'Educational Ads Pune',
    'IT Company Ads Pune',
    'Manufacturing Ads Pune',
    'Healthcare Ads Pune',
    'Real Estate Ads Pune',
    'Automotive Ads Pune',
    'Startup Ads Pune',
    'B2B Lead Generation Pune',
    'Conversion Optimization Pune'
  ];

  const adSpecializations = [
    {
      type: 'Educational Institution Ads',
      description: 'Targeted advertising for Pune\'s educational sector with student acquisition focus',
      icon: Building,
      features: ['Student Lead Generation', 'Course Promotion', 'Admission Campaigns', 'Parent Targeting']
    },
    {
      type: 'IT & Software Company Ads',
      description: 'B2B advertising for Pune\'s IT corridor and software companies',
      icon: Target,
      features: ['B2B Lead Generation', 'Software Promotion', 'Tech Recruitment', 'Global Targeting']
    },
    {
      type: 'Manufacturing & Industrial Ads',
      description: 'Industrial advertising for Pune\'s manufacturing and automotive sector',
      icon: Zap,
      features: ['Industrial Lead Gen', 'B2B Manufacturing', 'Export Promotion', 'Supply Chain Ads']
    },
    {
      type: 'Startup Growth Ads',
      description: 'Performance marketing for Pune\'s vibrant startup ecosystem',
      icon: Star,
      features: ['Startup Marketing', 'Growth Hacking', 'Investor Outreach', 'User Acquisition']
    }
  ];

  const adPackages = [
    {
      name: 'Google Ads Pune Starter',
      price: '₹20,000',
      period: '/month',
      description: 'Perfect for small Pune businesses and startups',
      features: [
        'Ad Spend: ₹15,000/month',
        'Search + Display Campaigns',
        'Local Pune Targeting',
        'Landing Page Optimization',
        'Monthly Performance Reports',
        'Educational Sector Focus'
      ]
    },
    {
      name: 'Pune Google Ads Professional',
      price: '₹35,000',
      period: '/month',
      description: 'Comprehensive advertising for growing Pune businesses',
      features: [
        'Ad Spend: ₹50,000/month',
        'Multi-Campaign Strategy',
        'Advanced Targeting',
        'Conversion Tracking',
        'A/B Testing',
        'IT Sector Specialization',
        'Bi-weekly Optimization'
      ],
      popular: true
    },
    {
      name: 'Enterprise Google Ads Pune',
      price: '₹65,000',
      period: '/month',
      description: 'Advanced advertising for large Pune enterprises',
      features: [
        'Ad Spend: ₹1,00,000+/month',
        'Full-Funnel Campaigns',
        'Advanced Analytics',
        'Multi-platform Integration',
        'Dedicated Account Manager',
        'Weekly Strategy Calls',
        'Priority Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '1800+',
      description: 'Pune Ad Campaigns',
      detail: 'Successfully managed'
    },
    {
      metric: '520%',
      description: 'Average ROAS',
      detail: 'Return on ad spend'
    },
    {
      metric: '₹32Cr+',
      description: 'Revenue Generated',
      detail: 'Through Google Ads'
    },
    {
      metric: '89%',
      description: 'Lead Quality Score',
      detail: 'High-intent prospects'
    }
  ];

  const achievements = [
    'Top Google Ads Agency in Pune',
    'Educational Marketing Specialists',
    'IT Sector PPC Leaders',
    'Manufacturing Ad Experts',
    'Startup Growth Champions',
    'Google Premier Partner'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-6 py-2 mb-8">
                    <Target className="w-4 h-4 text-green-400" />
                    <span className="text-green-400 font-medium">Google Ads Pune • Oxford of the East Advertising</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Google Ads Company in
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Pune</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier Google Ads management in Pune offering comprehensive PPC advertising for educational institutions, IT companies, and manufacturing businesses. Serving 1800+ Pune businesses across all areas - from educational hubs in Kothrud to IT corridors in Hinjewadi. Expert Google Ads solutions with proven ₹32Cr+ revenue generation and 520% average ROAS for Pune clients in Maharashtra's Oxford of the East.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Pune Ads Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call PPC Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Pune Google Ads
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable Google Ads results from Pune businesses across educational, IT, and manufacturing sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-green-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Ad Specializations Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Pune Google Ads
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Specializations</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized advertising strategies designed for Pune's unique educational, IT, and industrial landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {adSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Pune Google Ads
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive Google Ads pricing designed for Pune's educational and IT business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {adPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-green-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-green-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-green-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-green-600 to-green-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Pune with Google Ads?</h2>
                <p className="text-xl mb-8">
                  Join 1800+ Pune businesses that trust GOD Digital Marketing for Google Ads success. Proven strategies that deliver 520% ROAS and ₹32Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-green-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Ads Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 text-lg px-8 py-4">
                    <Link to="/contact">Call PPC Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="ppc" currentLocation="pune" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default GoogleAdsPune;
