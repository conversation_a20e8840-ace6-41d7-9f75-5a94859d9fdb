import React from 'react';
import { Link } from 'react-router-dom';
import { MapPin, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const TamilNadu = () => {
  const stats = [
    {
      metric: '3,800+',
      description: 'Tamil Nadu Businesses Served',
      detail: 'Across all major cities'
    },
    {
      metric: '3,900%',
      description: 'Average Business Growth',
      detail: 'For Tamil Nadu clients'
    },
    {
      metric: '₹1,280Cr+',
      description: 'Tamil Nadu Revenue Generated',
      detail: 'Through digital marketing'
    },
    {
      metric: '95%',
      description: 'Client Retention Rate',
      detail: 'Long-term partnerships'
    }
  ];

  const achievements = [
    'Top Digital Marketing Company in Tamil Nadu',
    'Chennai IT SEO Specialists',
    'Coimbatore Textile Marketing Experts',
    'Madurai Manufacturing Leaders',
    'Salem Steel Industry Champions',
    'Tamil Nadu Automotive SEO Pioneers'
  ];

  const tamilnaduCities = [
    {
      city: 'Chennai',
      description: 'Detroit of India with automotive, IT, healthcare, and port industries',
      businesses: '1,600+',
      growth: '4,200%',
      specializations: ['Automotive SEO', 'IT Services SEO', 'Healthcare SEO', 'Port & Shipping SEO']
    },
    {
      city: 'Coimbatore',
      description: 'Textile capital with cotton mills, engineering, and pump manufacturing',
      businesses: '720+',
      growth: '3,800%',
      specializations: ['Textile Industry SEO', 'Engineering SEO', 'Pump Manufacturing SEO', 'Cotton Trading SEO']
    },
    {
      city: 'Madurai',
      description: 'Temple city with textile, granite, and agricultural industries',
      businesses: '580+',
      growth: '3,600%',
      specializations: ['Textile Manufacturing SEO', 'Granite Industry SEO', 'Agricultural SEO', 'Tourism SEO']
    },
    {
      city: 'Salem',
      description: 'Steel city with steel production, textiles, and magnesite mining',
      businesses: '480+',
      growth: '3,400%',
      specializations: ['Steel Industry SEO', 'Mining SEO', 'Textile SEO', 'Manufacturing SEO']
    }
  ];

  const tamilnaduIndustries = [
    {
      industry: 'Automotive & Manufacturing',
      description: 'Comprehensive digital marketing for Tamil Nadu\'s automotive and manufacturing sectors',
      icon: Building,
      features: ['Automotive Manufacturing SEO', 'Auto Components SEO', 'Heavy Machinery SEO', 'Industrial Equipment SEO']
    },
    {
      industry: 'Textile & Garment Industry',
      description: 'Strategic digital marketing for Tamil Nadu\'s dominant textile and garment industry',
      icon: Star,
      features: ['Textile Manufacturing SEO', 'Garment Export SEO', 'Cotton Industry SEO', 'Fashion Brand SEO']
    },
    {
      industry: 'IT & Software Services',
      description: 'Advanced marketing for Tamil Nadu\'s growing IT and software services sector',
      icon: Crown,
      features: ['Software Development SEO', 'IT Services SEO', 'Tech Startup SEO', 'Digital Services SEO']
    },
    {
      industry: 'Healthcare & Pharmaceuticals',
      description: 'Specialized marketing for Tamil Nadu\'s healthcare and pharmaceutical industries',
      icon: Target,
      features: ['Hospital SEO', 'Pharmaceutical SEO', 'Medical Device SEO', 'Healthcare Services SEO']
    }
  ];

  const caseStudies = [
    {
      client: 'Leading Automotive Manufacturer',
      city: 'Chennai',
      industry: 'Automotive Manufacturing',
      challenge: 'Auto manufacturer needed to establish global supply chain presence and attract international buyers',
      result: '4,500% international business growth',
      metrics: ['3,200+ automotive keywords in top 3', '₹520Cr+ automotive revenue', '780% increase in global partnerships']
    },
    {
      client: 'Textile Export Company',
      city: 'Coimbatore',
      industry: 'Textile Manufacturing',
      challenge: 'Textile company needed to compete in global markets and establish digital export presence',
      result: '4,100% export order increase',
      metrics: ['2,800+ textile keywords ranking', '₹385Cr+ export revenue', '650% increase in international orders']
    },
    {
      client: 'Healthcare Chain',
      city: 'Chennai',
      industry: 'Healthcare Services',
      challenge: 'Hospital chain needed to attract medical tourism and establish regional healthcare leadership',
      result: '3,800% patient inquiry growth',
      metrics: ['2,200+ healthcare keywords in top 5', '₹285Cr+ healthcare revenue', '580% increase in medical tourism']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-red-500/20 border border-red-500/30 rounded-full px-6 py-2 mb-8">
                    <MapPin className="w-4 h-4 text-red-400" />
                    <span className="text-red-400 font-medium">Tamil Nadu Digital Marketing • Industrial Powerhouse Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Digital Marketing Company in
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Tamil Nadu</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier digital marketing services in Tamil Nadu offering comprehensive SEO, PPC, social media marketing, and web development solutions for automotive manufacturers, textile companies, IT firms, and healthcare providers. Our Tamil Nadu digital marketing agency provides professional services across Chennai, Coimbatore, Madurai, and Salem with automotive SEO, textile industry SEO, IT services SEO, and healthcare SEO. Serving 3,800+ Tamil Nadu businesses with proven ₹1,280Cr+ revenue generation and 3,900% average business growth through strategic digital marketing and industrial sector expertise in India's manufacturing hub state.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Tamil Nadu Business Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-red-500 text-red-400 hover:bg-red-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Tamil Nadu Experts: +91-**********</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-red-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Tamil Nadu Digital Marketing
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable digital marketing results from businesses across Tamil Nadu's major industrial and manufacturing cities.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-red-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Tamil Nadu Major Cities We
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Serve</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Comprehensive digital marketing services across Tamil Nadu's key industrial and manufacturing centers.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {tamilnaduCities.map((city, index) => (
                    <Card key={index} className="bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-4">{city.city}</h3>
                        <p className="text-slate-300 mb-6">{city.description}</p>
                        <div className="grid grid-cols-2 gap-4 mb-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-red-400">{city.businesses}</div>
                            <div className="text-slate-400 text-sm">Businesses Served</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-400">{city.growth}</div>
                            <div className="text-slate-400 text-sm">Average Growth</div>
                          </div>
                        </div>
                        <ul className="space-y-2">
                          {city.specializations.map((spec, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-red-400 mr-2 flex-shrink-0" />
                              {spec}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Tamil Nadu Industry-Specific
                    <span className="bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"> Digital Solutions</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized digital marketing strategies tailored for Tamil Nadu's key industries and manufacturing sectors.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {tamilnaduIndustries.map((industry, index) => (
                    <Card key={index} className="bg-slate-900/80 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-lg flex items-center justify-center mb-6">
                          <industry.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{industry.industry}</h3>
                        <p className="text-slate-300 mb-6">{industry.description}</p>
                        <ul className="space-y-2">
                          {industry.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-red-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Tamil Nadu Digital Marketing Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-red-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-1">{study.city} • {study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-red-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Services by City */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Services Across Tamil Nadu
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <Link to="/services/seo/chennai" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">SEO Services Chennai</h4>
                    <p className="text-slate-400 text-sm">Search engine optimization for Chennai automotive and IT companies</p>
                  </Link>
                  <Link to="/services/ppc/coimbatore" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Google Ads Coimbatore</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for Coimbatore textile and engineering businesses</p>
                  </Link>
                  <Link to="/services/social-media/madurai" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Social Media Madurai</h4>
                    <p className="text-slate-400 text-sm">Social media marketing for Madurai textile and tourism businesses</p>
                  </Link>
                  <Link to="/services/web-development/salem" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Web Development Salem</h4>
                    <p className="text-slate-400 text-sm">Website development for Salem steel and manufacturing companies</p>
                  </Link>
                  <Link to="/services/content/tirupur" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Content Marketing Tirupur</h4>
                    <p className="text-slate-400 text-sm">Content creation for Tirupur garment export businesses</p>
                  </Link>
                  <Link to="/services/email/erode" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Email Marketing Erode</h4>
                    <p className="text-slate-400 text-sm">Email campaigns for Erode textile and agricultural businesses</p>
                  </Link>
                  <Link to="/industries/automotive" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Automotive Industry SEO</h4>
                    <p className="text-slate-400 text-sm">Specialized SEO for Tamil Nadu's automotive industry</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-red-400 font-semibold mb-2 group-hover:text-red-300">Tamil Nadu Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our Tamil Nadu clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-red-600 to-red-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Tamil Nadu's Industrial Markets?</h2>
                <p className="text-xl mb-8">
                  Join 3,800+ successful Tamil Nadu businesses that trust GOD Digital Marketing for industrial excellence. Proven strategies delivering 3,900% growth and ₹1,280Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-red-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Tamil Nadu Business Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-red-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Tamil Nadu Experts: +91-**********</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="location" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default TamilNadu;
