
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowR<PERSON>, CheckCircle, Star, Play, TrendingUp, Users, Award, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import DigitalValueStream from '@/components/DigitalValueStream';
import Hero from '@/components/home/<USER>';
import ServicesOverview from '@/components/home/<USER>';
import ResultsSection from '@/components/home/<USER>';
import TestimonialsSection from '@/components/home/<USER>';
import BlogPreview from '@/components/home/<USER>';

const Index = () => {
  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <DigitalValueStream />
      <Navigation />
      
      <main className="relative z-10">
        <Hero />
        <ServicesOverview />
        <ResultsSection />
        <TestimonialsSection />
        <BlogPreview />
        
        {/* Final CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500 relative">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="container mx-auto px-6 text-center relative z-10">
            <h2 className="text-4xl font-bold mb-6">Ready to Dominate Global Markets?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join the elite businesses investing ₹60K-150K monthly for international SEO dominance. 7 years of proven results across 6 countries.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Premium SEO Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Book International Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Index;
