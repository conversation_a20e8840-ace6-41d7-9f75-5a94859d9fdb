
import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, CheckCircle, Star, Play, TrendingUp, Users, Award, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import DigitalValueStream from '@/components/DigitalValueStream';
import TestimonialsSection from '@/components/TestimonialsSection';
import IndustryShowcase from '@/components/IndustryShowcase';
import ImmersiveHero from '@/components/ImmersiveHero';
import ServicesOverview from '@/components/home/<USER>';
import ResultsSection from '@/components/home/<USER>';
import BlogPreview from '@/components/home/<USER>';
import FloatingActionButton from '@/components/FloatingActionButton';

const Index = () => {
  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <DigitalValueStream />
      <Navigation />
      
      <main className="relative z-10">
        <ImmersiveHero />
        <ServicesOverview />
        <ResultsSection />
        <IndustryShowcase />
        <TestimonialsSection />
        <BlogPreview />
        
        {/* Final CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500 relative overflow-hidden">
          {/* Animated background elements */}
          <div className="absolute inset-0 bg-black/20"></div>
          <motion.div
            className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"
            animate={{
              x: [0, 50, 0],
              y: [0, -30, 0],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute bottom-10 right-10 w-24 h-24 bg-white/10 rounded-full blur-xl"
            animate={{
              x: [0, -40, 0],
              y: [0, 20, 0],
              scale: [1, 0.8, 1],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
          />

          <div className="container mx-auto px-6 text-center relative z-10">
            <motion.h2
              className="text-4xl font-bold mb-6"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              Ready to Scale Your Business with Digital Marketing?
            </motion.h2>
            <motion.p
              className="text-xl mb-8 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              Partner with India's leading digital marketing agency. Our comprehensive digital marketing services include SEO, Google Ads, social media marketing, content marketing, email marketing, and web development. 7 years proven track record delivering results across 6 countries with ₹60K-150K monthly investment plans.
            </motion.p>
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4 shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <Link to="/quote" className="flex items-center gap-2">
                    Get Digital Marketing Strategy
                    <motion.div
                      animate={{ x: [0, 5, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                    >
                      <ArrowRight className="w-5 h-5" />
                    </motion.div>
                  </Link>
                </Button>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4 backdrop-blur-sm">
                  <Link to="/contact">Book Digital Marketing Consultation</Link>
                </Button>
              </motion.div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
      <FloatingActionButton />
    </div>
  );
};

export default Index;
