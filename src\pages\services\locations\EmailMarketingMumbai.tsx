import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Mail, TrendingUp, Users, CheckCircle, Building, Star, Crown, Send, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const EmailMarketingMumbai = () => {
  const emailServices = [
    'Email Marketing Strategy Mumbai',
    'Email Campaign Management Mumbai',
    'Email Automation Mumbai',
    'Newsletter Marketing Mumbai',
    'Drip Campaign Setup Mumbai',
    'Email Template Design Mumbai',
    'Email List Building Mumbai',
    'Email Segmentation Mumbai',
    'Personalized Email Marketing Mumbai',
    'Transactional Email Setup Mumbai',
    'Email A/B Testing Mumbai',
    'Email Analytics & Reporting Mumbai',
    'Email Deliverability Optimization Mumbai',
    'CRM Email Integration Mumbai',
    'E-commerce Email Marketing Mumbai',
    'Lead Nurturing Emails Mumbai',
    'Financial Services Email Mumbai',
    'Entertainment Industry Email Mumbai',
    'Re-engagement Campaigns Mumbai',
    'Email Compliance Management Mumbai'
  ];

  const mumbaiAreas = [
    'South Mumbai Email Marketing',
    'Bandra Email Services',
    'Andheri Email Campaigns',
    'Powai Email Marketing',
    'Worli Email Services',
    'Lower Parel Email Campaigns',
    'BKC Email Marketing',
    'Malad Email Services',
    'Goregaon Email Campaigns',
    'Thane Email Marketing',
    'Navi Mumbai Email Services',
    'Borivali Email Campaigns'
  ];

  const emailTypes = [
    {
      type: 'Financial Newsletter Campaigns',
      description: 'Specialized newsletters for Mumbai\'s financial district and banking sector',
      icon: Mail,
      features: ['Market Updates', 'Investment Insights', 'Financial News', 'Compliance Content']
    },
    {
      type: 'Entertainment Industry Emails',
      description: 'Creative email campaigns for Mumbai\'s entertainment and media industry',
      icon: Star,
      features: ['Event Promotions', 'Celebrity Updates', 'Show Announcements', 'Fan Engagement']
    },
    {
      type: 'Automated Drip Campaigns',
      description: 'Automated email sequences that nurture leads and drive conversions',
      icon: Zap,
      features: ['Welcome Series', 'Lead Nurturing', 'Onboarding Sequences', 'Re-engagement Campaigns']
    },
    {
      type: 'Transactional Emails',
      description: 'Essential transactional emails that enhance customer experience',
      icon: Send,
      features: ['Order Confirmations', 'Shipping Updates', 'Password Resets', 'Account Notifications']
    }
  ];

  const emailPackages = [
    {
      name: 'Email Marketing Mumbai Starter',
      price: '₹18,000',
      period: '/month',
      description: 'Perfect for small Mumbai businesses starting with email marketing',
      features: [
        'Up to 5,000 Subscribers',
        '4 Email Campaigns per Month',
        'Basic Email Templates',
        'List Management',
        'Basic Analytics',
        'Email Support'
      ]
    },
    {
      name: 'Mumbai Email Marketing Professional',
      price: '₹35,000',
      period: '/month',
      description: 'Comprehensive email marketing for growing Mumbai businesses',
      features: [
        'Up to 25,000 Subscribers',
        'Unlimited Email Campaigns',
        'Custom Email Templates',
        'Advanced Automation',
        'A/B Testing',
        'Advanced Analytics',
        'Phone Support'
      ],
      popular: true
    },
    {
      name: 'Enterprise Email Marketing Mumbai',
      price: '₹65,000',
      period: '/month',
      description: 'Advanced email marketing for large Mumbai enterprises',
      features: [
        'Unlimited Subscribers',
        'Advanced Automation Workflows',
        'Custom Integrations',
        'Dedicated IP',
        'Advanced Segmentation',
        'Dedicated Account Manager',
        'Priority Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '1.5M+',
      description: 'Emails Sent Monthly',
      detail: 'For Mumbai businesses'
    },
    {
      metric: '32%',
      description: 'Average Open Rate',
      detail: 'Above industry standard'
    },
    {
      metric: '₹25Cr+',
      description: 'Revenue Generated',
      detail: 'Through email campaigns'
    },
    {
      metric: '96%',
      description: 'Deliverability Rate',
      detail: 'Consistent inbox placement'
    }
  ];

  const achievements = [
    'Top Email Marketing Agency in Mumbai',
    'Financial Services Email Specialists',
    'Entertainment Industry Experts',
    'Mailchimp Certified Partners',
    'HubSpot Email Marketing Experts',
    'High Deliverability Champions'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-5xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-cyan-500/20 border border-cyan-500/30 rounded-full px-6 py-2 mb-8">
                <Mail className="w-4 h-4 text-cyan-400" />
                <span className="text-cyan-400 font-medium">Email Marketing Mumbai • Financial Capital Direct Communication</span>
              </div>
              
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                #1 Email Marketing Company in
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Mumbai</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto">
                Premier email marketing in Mumbai offering comprehensive email campaign management and automation services. Serving 600+ Mumbai businesses across all areas - from financial services in BKC to entertainment industry in Andheri. Expert email marketing solutions with proven ₹25Cr+ revenue generation and 1.5M+ emails sent monthly for Mumbai clients in India's commercial capital.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Mumbai Email Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Email Experts: +91-9999-XXXX-XX</Link>
                </Button>
              </div>

              {/* Achievements */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                    <CheckCircle className="w-4 h-4 text-cyan-400 flex-shrink-0" />
                    <span className="text-slate-300">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Mumbai Email Marketing
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Success Metrics</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12">
                Measurable email marketing results from Mumbai businesses across all industries - delivering exceptional engagement in India's financial capital.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-cyan-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Email Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Email Campaign Types We
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Master</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Specialized email marketing campaigns designed for Mumbai's financial, entertainment, and business landscape.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-20">
              {emailTypes.map((email, index) => (
                <Card key={index} className="bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center mb-6">
                      <email.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{email.type}</h3>
                    <p className="text-slate-300 mb-6">{email.description}</p>
                    <ul className="space-y-2">
                      {email.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-cyan-400 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Complete Email Marketing Services
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Available in Mumbai</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive range of email marketing services covering every aspect of email campaigns for Mumbai businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {emailServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Mail className="w-8 h-8 text-cyan-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Mumbai Email Marketing
                <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Pricing</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Premium email marketing pricing designed for Mumbai's competitive business environment.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {emailPackages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-cyan-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-cyan-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-cyan-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-cyan-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-cyan-600 to-cyan-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">Ready to Dominate Mumbai Inboxes?</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto">
              Join 600+ Mumbai businesses that trust GOD Digital Marketing for email marketing success. Proven strategies that deliver 32% open rates and ₹25Cr+ revenue generation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-cyan-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Email Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-cyan-600 text-lg px-8 py-4">
                <Link to="/contact">Call Email Experts: +91-9999-XXXX-XX</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default EmailMarketingMumbai;
