import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Globe, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SeoServicesPune = () => {
  const seoServices = [
    'SEO Services Pune',
    'Local SEO Pune',
    'International SEO Pune',
    'E-commerce SEO Pune',
    'Technical SEO Pune',
    'Enterprise SEO Pune',
    'SEO Audit Pune',
    'Keyword Research Pune',
    'On-Page SEO Pune',
    'Off-Page SEO Pune',
    'Link Building Pune',
    'Content SEO Pune',
    'Mobile SEO Pune',
    'Voice Search SEO Pune',
    'Educational SEO Pune',
    'IT Company SEO Pune',
    'Manufacturing SEO Pune',
    'Automotive SEO Pune',
    'Healthcare SEO Pune',
    'Real Estate SEO Pune'
  ];

  const seoSpecializations = [
    {
      type: 'Educational Institution SEO',
      description: 'Specialized SEO for Pune\'s educational hub with 100+ colleges and universities',
      icon: Building,
      features: ['University SEO', 'College Marketing', 'Student Acquisition', 'Academic Rankings']
    },
    {
      type: 'IT & Software Company SEO',
      description: 'Advanced SEO for Pune\'s thriving IT sector and software companies',
      icon: Globe,
      features: ['Tech Company SEO', 'Software Marketing', 'B2B Lead Generation', 'Global Reach']
    },
    {
      type: 'Manufacturing & Industrial SEO',
      description: 'Industrial SEO for Pune\'s manufacturing and automotive sector',
      icon: Zap,
      features: ['Industrial SEO', 'B2B Manufacturing', 'Export Marketing', 'Supply Chain SEO']
    },
    {
      type: 'Startup & Innovation SEO',
      description: 'Growth-focused SEO for Pune\'s vibrant startup ecosystem',
      icon: Star,
      features: ['Startup SEO', 'Innovation Marketing', 'Investor Outreach', 'Growth Hacking']
    }
  ];

  const seoPackages = [
    {
      name: 'SEO Pune Starter',
      price: '₹25,000',
      period: '/month',
      description: 'Perfect for small Pune businesses and startups',
      features: [
        'Local Pune SEO Optimization',
        'Google My Business Setup',
        '20 Target Keywords',
        'On-Page SEO (10 pages)',
        'Monthly Reporting',
        'Educational Sector Focus'
      ]
    },
    {
      name: 'Pune SEO Professional',
      price: '₹45,000',
      period: '/month',
      description: 'Comprehensive SEO for growing Pune businesses',
      features: [
        'Advanced Local + National SEO',
        '50 Target Keywords',
        'Technical SEO Audit',
        'Content Marketing Strategy',
        'Link Building Campaign',
        'IT Sector Specialization',
        'Bi-weekly Reporting'
      ],
      popular: true
    },
    {
      name: 'Enterprise SEO Pune',
      price: '₹85,000',
      period: '/month',
      description: 'Advanced SEO for large Pune enterprises and multinationals',
      features: [
        'International SEO Strategy',
        'Unlimited Keywords',
        'Advanced Technical SEO',
        'Multi-language SEO',
        'Enterprise Analytics',
        'Dedicated SEO Manager',
        'Weekly Reporting'
      ]
    }
  ];

  const stats = [
    {
      metric: '2400+',
      description: 'Pune Businesses Ranked',
      detail: 'Across all sectors'
    },
    {
      metric: '850%',
      description: 'Average Traffic Increase',
      detail: 'For Pune clients'
    },
    {
      metric: '₹45Cr+',
      description: 'Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '96%',
      description: 'Client Retention Rate',
      detail: 'Long-term partnerships'
    }
  ];

  const achievements = [
    'Top SEO Company in Pune',
    'Educational SEO Specialists',
    'IT Sector SEO Leaders',
    'Manufacturing SEO Experts',
    'Startup Growth Champions',
    'International SEO Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-blue-500/20 border border-blue-500/30 rounded-full px-6 py-2 mb-8">
                    <Search className="w-4 h-4 text-blue-400" />
                    <span className="text-blue-400 font-medium">SEO Services Pune • Oxford of the East Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 SEO Company in
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Pune</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier SEO services in Pune offering comprehensive search engine optimization for educational institutions, IT companies, and manufacturing businesses. Serving 2400+ Pune businesses across all areas - from educational hubs in Kothrud to IT corridors in Hinjewadi. Expert SEO solutions with proven ₹45Cr+ revenue generation and 850% average traffic increase for Pune clients in Maharashtra's Oxford of the East.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Pune SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-blue-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Pune SEO
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable SEO results from Pune businesses across educational, IT, and manufacturing sectors.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-blue-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* SEO Specializations Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Pune SEO
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Specializations</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized SEO strategies designed for Pune's unique educational, IT, and industrial landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {seoSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-blue-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Pune SEO
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive SEO pricing designed for Pune's educational and IT business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {seoPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-blue-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-blue-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-blue-600 to-blue-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Pune Search Results?</h2>
                <p className="text-xl mb-8">
                  Join 2400+ Pune businesses that trust GOD Digital Marketing for SEO success. Proven strategies that deliver 850% traffic increase and ₹45Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4">
                    <Link to="/contact">Call SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="seo" currentLocation="pune" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoServicesPune;
