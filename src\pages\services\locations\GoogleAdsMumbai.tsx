import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Target, TrendingUp, Users, CheckCircle, Building, Star, Crown, Zap, DollarSign } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const GoogleAdsMumbai = () => {
  const googleAdsServices = [
    'Google Search Ads Mumbai',
    'Google Display Ads Mumbai',
    'Google Shopping Ads Mumbai',
    'YouTube Ads Management Mumbai',
    'Google App Campaigns Mumbai',
    'Performance Max Campaigns Mumbai',
    'Local Campaigns Mumbai',
    'Smart Campaigns Mumbai',
    'Discovery Ads Mumbai',
    'Demand Gen Campaigns Mumbai',
    'Google Ads Remarketing Mumbai',
    'Dynamic Search Ads Mumbai',
    'Responsive Search Ads Mumbai',
    'Call-Only Campaigns Mumbai',
    'Video Action Campaigns Mumbai',
    'Financial Services Ads Mumbai',
    'Entertainment Industry Ads Mumbai',
    'Real Estate Ads Mumbai',
    'E-commerce Ads Mumbai',
    'B2B Lead Generation Mumbai'
  ];

  const mumbaiAreas = [
    'South Mumbai Google Ads',
    'Bandra PPC Services',
    'Andheri Google Ads',
    'Powai PPC Management',
    'Worli Google Ads',
    'Lower Parel PPC Services',
    'BKC Google Ads',
    'Malad PPC Management',
    'Goregaon Google Ads',
    'Thane PPC Services',
    'Navi Mumbai Google Ads',
    'Borivali PPC Management'
  ];

  const industries = [
    'Financial Services Google Ads Mumbai',
    'Entertainment Industry Google Ads Mumbai',
    'Real Estate Google Ads Mumbai',
    'Healthcare Google Ads Mumbai',
    'Education Google Ads Mumbai',
    'E-commerce Google Ads Mumbai',
    'IT Services Google Ads Mumbai',
    'Manufacturing Google Ads Mumbai',
    'Textile Industry Google Ads Mumbai',
    'Diamond & Jewelry Google Ads Mumbai',
    'Hospitality Google Ads Mumbai',
    'Logistics Google Ads Mumbai'
  ];

  const googleAdsPackages = [
    {
      name: 'Google Ads Mumbai Starter',
      price: '₹25,000',
      period: '/month',
      adSpend: '+ Ad Spend',
      description: 'Perfect for small Mumbai businesses starting with Google Ads',
      features: [
        'Google Search Ads Setup',
        'Keyword Research (50 keywords)',
        'Ad Copy Creation (10 ads)',
        'Landing Page Optimization',
        'Conversion Tracking Setup',
        'Monthly Performance Report'
      ]
    },
    {
      name: 'Mumbai Google Ads Professional',
      price: '₹45,000',
      period: '/month',
      adSpend: '+ Ad Spend',
      description: 'Comprehensive Google Ads management for growing Mumbai businesses',
      features: [
        'Everything in Starter',
        'Display & Shopping Ads',
        'YouTube Ads Management',
        'Remarketing Campaigns',
        'Advanced Audience Targeting',
        'A/B Testing & Optimization',
        'Weekly Performance Reviews'
      ],
      popular: true
    },
    {
      name: 'Enterprise Google Ads Mumbai',
      price: '₹80,000',
      period: '/month',
      adSpend: '+ Ad Spend',
      description: 'Advanced Google Ads management for large Mumbai enterprises',
      features: [
        'Everything in Professional',
        'Performance Max Campaigns',
        'Advanced Automation Setup',
        'Custom Audience Creation',
        'Cross-Platform Integration',
        'Dedicated Account Manager',
        'Priority Support'
      ]
    }
  ];

  const campaignTypes = [
    {
      type: 'Financial Services Campaigns',
      description: 'Specialized campaigns for Mumbai\'s financial district targeting banking and finance customers',
      icon: DollarSign,
      benefits: ['High-Value Leads', 'Compliance-Ready', 'Trust Building', 'ROI Focused']
    },
    {
      type: 'Entertainment Industry Ads',
      description: 'Creative campaigns for Mumbai\'s entertainment and media industry',
      icon: Star,
      benefits: ['Creative Targeting', 'Audience Engagement', 'Brand Building', 'Event Promotion']
    },
    {
      type: 'Real Estate Campaigns',
      description: 'Property-focused campaigns targeting Mumbai\'s premium real estate market',
      icon: Building,
      benefits: ['Location Targeting', 'High-Value Properties', 'Investor Targeting', 'Lead Quality']
    },
    {
      type: 'E-commerce Shopping Ads',
      description: 'Product-focused campaigns for Mumbai\'s thriving e-commerce businesses',
      icon: Zap,
      benefits: ['Product Visibility', 'Shopping Intent', 'Local Delivery', 'Conversion Focus']
    }
  ];

  const stats = [
    {
      metric: '1800+',
      description: 'Mumbai Google Ads Campaigns',
      detail: 'Successfully managed'
    },
    {
      metric: '520%',
      description: 'Average ROAS Achieved',
      detail: 'For Mumbai businesses'
    },
    {
      metric: '₹45Cr+',
      description: 'Revenue Generated',
      detail: 'Through Google Ads'
    },
    {
      metric: '68%',
      description: 'Cost Reduction',
      detail: 'Average CPC optimization'
    }
  ];

  const achievements = [
    'Google Premier Partner Mumbai',
    'Google Ads Certified Specialists',
    'Top PPC Agency in Mumbai',
    'Financial Services Ad Experts',
    'Entertainment Industry Partners',
    'Performance Max Champions'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-5xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-6 py-2 mb-8">
                <Target className="w-4 h-4 text-green-400" />
                <span className="text-green-400 font-medium">Google Ads Mumbai • Financial Capital PPC Excellence</span>
              </div>
              
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                #1 Google Ads Company in
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Mumbai</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto">
                Premier Google Ads management in Mumbai offering comprehensive PPC solutions from search campaigns to YouTube advertising. Serving 1800+ Mumbai businesses across all areas - from financial services in BKC to entertainment industry in Andheri. Expert Google Ads solutions with proven ₹45Cr+ revenue generation and 520% average ROAS for Mumbai clients in India's commercial capital.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Mumbai Google Ads Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Mumbai PPC Experts: +91-9999-XXXX-XX</Link>
                </Button>
              </div>

              {/* Achievements */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                    <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                    <span className="text-slate-300">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Mumbai Google Ads
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Success Metrics</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12">
                Measurable Google Ads results from Mumbai businesses across all industries - delivering exceptional ROAS in India's most competitive market.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-green-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Campaign Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Google Ads Campaign Types We
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Manage</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Specialized Google Ads campaign management for Mumbai's diverse business landscape and competitive market.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-20">
              {campaignTypes.map((campaign, index) => (
                <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-6">
                      <campaign.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{campaign.type}</h3>
                    <p className="text-slate-300 mb-6">{campaign.description}</p>
                    <ul className="space-y-2">
                      {campaign.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Google Ads Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Complete Google Ads Services
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Available in Mumbai</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive range of Google Ads services covering every aspect of PPC advertising for Mumbai businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {googleAdsServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Target className="w-8 h-8 text-green-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Mumbai Google Ads Management
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Pricing</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Premium Google Ads management pricing designed for Mumbai's competitive business environment.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {googleAdsPackages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-green-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-green-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-green-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                      <div className="text-sm text-slate-400">{pkg.adSpend}</div>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-green-600 to-green-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">Ready to Dominate Mumbai with Google Ads?</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto">
              Join 1800+ Mumbai businesses that trust GOD Digital Marketing for Google Ads success. Proven strategies that deliver 520% ROAS and ₹45Cr+ revenue generation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-green-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Google Ads Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 text-lg px-8 py-4">
                <Link to="/contact">Call PPC Experts: +91-9999-XXXX-XX</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default GoogleAdsMumbai;
