import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Target, TrendingUp, Users, CheckCircle, Building, Star, Crown, Zap, DollarSign } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const GoogleAdsChennai = () => {
  const googleAdsServices = [
    'Google Search Ads Chennai',
    'Google Display Ads Chennai',
    'Google Shopping Ads Chennai',
    'YouTube Ads Management Chennai',
    'Google App Campaigns Chennai',
    'Performance Max Campaigns Chennai',
    'Local Campaigns Chennai',
    'Smart Campaigns Chennai',
    'Discovery Ads Chennai',
    'Demand Gen Campaigns Chennai',
    'Google Ads Remarketing Chennai',
    'Dynamic Search Ads Chennai',
    'Responsive Search Ads Chennai',
    'Call-Only Campaigns Chennai',
    'Video Action Campaigns Chennai',
    'Automotive Ads Chennai',
    'Healthcare Ads Chennai',
    'Manufacturing Ads Chennai',
    'B2B Lead Generation Chennai',
    'Export Business Ads Chennai'
  ];

  const campaignTypes = [
    {
      type: 'Automotive Industry Campaigns',
      description: 'Specialized campaigns for Chennai\'s automotive and manufacturing sector',
      icon: Building,
      benefits: ['Automotive Targeting', 'B2B Lead Generation', 'Manufacturing Focus', 'Export Promotion']
    },
    {
      type: 'Healthcare Marketing Ads',
      description: 'Medical and healthcare focused campaigns for Chennai\'s healthcare industry',
      icon: Star,
      benefits: ['Medical Services', 'Healthcare Products', 'Patient Acquisition', 'Trust Building']
    },
    {
      type: 'Manufacturing B2B Campaigns',
      description: 'Industrial and manufacturing focused campaigns for Chennai\'s industrial sector',
      icon: Zap,
      benefits: ['Industrial Products', 'B2B Targeting', 'Export Markets', 'Lead Quality']
    },
    {
      type: 'Local Business Campaigns',
      description: 'Location-based campaigns for Chennai\'s local businesses and services',
      icon: DollarSign,
      benefits: ['Local Targeting', 'Service Areas', 'Tamil Audience', 'Regional Focus']
    }
  ];

  const googleAdsPackages = [
    {
      name: 'Google Ads Chennai Starter',
      price: '₹24,000',
      period: '/month',
      adSpend: '+ Ad Spend',
      description: 'Perfect for small Chennai businesses starting with Google Ads',
      features: [
        'Google Search Ads Setup',
        'Keyword Research (50 keywords)',
        'Ad Copy Creation (10 ads)',
        'Landing Page Optimization',
        'Conversion Tracking Setup',
        'Monthly Performance Report'
      ]
    },
    {
      name: 'Chennai Google Ads Professional',
      price: '₹42,000',
      period: '/month',
      adSpend: '+ Ad Spend',
      description: 'Comprehensive Google Ads management for growing Chennai businesses',
      features: [
        'Everything in Starter',
        'Display & Shopping Ads',
        'YouTube Ads Management',
        'Remarketing Campaigns',
        'Advanced Audience Targeting',
        'A/B Testing & Optimization',
        'Weekly Performance Reviews'
      ],
      popular: true
    },
    {
      name: 'Enterprise Google Ads Chennai',
      price: '₹75,000',
      period: '/month',
      adSpend: '+ Ad Spend',
      description: 'Advanced Google Ads management for large Chennai enterprises',
      features: [
        'Everything in Professional',
        'Performance Max Campaigns',
        'Advanced Automation Setup',
        'Custom Audience Creation',
        'Cross-Platform Integration',
        'Dedicated Account Manager',
        'Priority Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '1300+',
      description: 'Chennai Google Ads Campaigns',
      detail: 'Successfully managed'
    },
    {
      metric: '460%',
      description: 'Average ROAS Achieved',
      detail: 'For Chennai businesses'
    },
    {
      metric: '₹35Cr+',
      description: 'Revenue Generated',
      detail: 'Through Google Ads'
    },
    {
      metric: '58%',
      description: 'Cost Reduction',
      detail: 'Average CPC optimization'
    }
  ];

  const achievements = [
    'Google Premier Partner Chennai',
    'Google Ads Certified Specialists',
    'Top PPC Agency in Chennai',
    'Automotive Industry Ad Experts',
    'Healthcare Marketing Specialists',
    'Manufacturing B2B Champions'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-full px-6 py-2 mb-8">
                    <Target className="w-4 h-4 text-green-400" />
                    <span className="text-green-400 font-medium">Google Ads Chennai • Detroit of India PPC Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Google Ads Company in
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Chennai</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier Google Ads management in Chennai offering comprehensive PPC solutions from search campaigns to YouTube advertising. Serving 1300+ Chennai businesses across all areas - from automotive companies in Ambattur to healthcare services in T. Nagar. Expert Google Ads solutions with proven ₹35Cr+ revenue generation and 460% average ROAS for Chennai clients in India's Detroit.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Chennai Google Ads Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Chennai PPC Experts: +91-9999-XXXX-XX</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Chennai Google Ads
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable Google Ads results from Chennai businesses across all industries.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-green-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Campaign Types Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Google Ads Campaign Types We
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Manage</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized Google Ads campaign management for Chennai's automotive and manufacturing landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {campaignTypes.map((campaign, index) => (
                    <Card key={index} className="bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-6">
                          <campaign.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{campaign.type}</h3>
                        <p className="text-slate-300 mb-6">{campaign.description}</p>
                        <ul className="space-y-2">
                          {campaign.benefits.map((benefit, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                              {benefit}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Chennai Google Ads Management
                    <span className="bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive Google Ads management pricing designed for Chennai's automotive and manufacturing business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {googleAdsPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-green-500/20 hover:border-green-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-green-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-green-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-green-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                          <div className="text-sm text-slate-400">{pkg.adSpend}</div>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-green-600 to-green-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Chennai with Google Ads?</h2>
                <p className="text-xl mb-8">
                  Join 1300+ Chennai businesses that trust GOD Digital Marketing for Google Ads success. Proven strategies that deliver 460% ROAS and ₹35Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-green-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Google Ads Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600 text-lg px-8 py-4">
                    <Link to="/contact">Call PPC Experts: +91-9999-XXXX-XX</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="ppc" currentLocation="chennai" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default GoogleAdsChennai;
