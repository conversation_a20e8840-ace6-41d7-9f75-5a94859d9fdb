import React from 'react';
import { Link } from 'react-router-dom';
import { MapPin, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const Karnataka = () => {
  const stats = [
    {
      metric: '3,200+',
      description: 'Karnataka Businesses Served',
      detail: 'Across all major cities'
    },
    {
      metric: '4,200%',
      description: 'Average Business Growth',
      detail: 'For Karnataka clients'
    },
    {
      metric: '₹1,150Cr+',
      description: 'Karnataka Revenue Generated',
      detail: 'Through digital marketing'
    },
    {
      metric: '96%',
      description: 'Client Retention Rate',
      detail: 'Long-term partnerships'
    }
  ];

  const achievements = [
    'Top Digital Marketing Company in Karnataka',
    'Bangalore IT SEO Specialists',
    'Mysore Tourism Marketing Experts',
    'Mangalore Port Business Leaders',
    'Hubli Manufacturing Champions',
    'Karnataka Tech Industry Pioneers'
  ];

  const karnatakaCities = [
    {
      city: 'Bangalore',
      description: 'India\'s Silicon Valley with thriving IT, biotech, and aerospace industries',
      businesses: '1,800+',
      growth: '4,500%',
      specializations: ['IT Services SEO', 'Software Company SEO', 'Biotech SEO', 'Aerospace SEO']
    },
    {
      city: 'Mysore',
      description: 'Cultural capital with tourism, silk industry, and educational institutions',
      businesses: '480+',
      growth: '3,800%',
      specializations: ['Tourism SEO', 'Silk Industry SEO', 'Educational Institution SEO', 'Heritage Tourism SEO']
    },
    {
      city: 'Mangalore',
      description: 'Major port city with shipping, petrochemicals, and banking industries',
      businesses: '420+',
      growth: '3,600%',
      specializations: ['Port & Shipping SEO', 'Petrochemical SEO', 'Banking SEO', 'Import-Export SEO']
    },
    {
      city: 'Hubli-Dharwad',
      description: 'Industrial hub with cotton trading, manufacturing, and educational centers',
      businesses: '380+',
      growth: '3,400%',
      specializations: ['Cotton Trading SEO', 'Manufacturing SEO', 'Agricultural SEO', 'Industrial Equipment SEO']
    }
  ];

  const karnatakaIndustries = [
    {
      industry: 'IT & Software Services',
      description: 'Comprehensive digital marketing for Karnataka\'s dominant IT and software industry',
      icon: Building,
      features: ['Software Company SEO', 'IT Services SEO', 'Tech Startup SEO', 'SaaS Platform SEO']
    },
    {
      industry: 'Biotechnology & Pharmaceuticals',
      description: 'Strategic digital marketing for Karnataka\'s biotech and pharmaceutical sectors',
      icon: Star,
      features: ['Biotech Company SEO', 'Pharmaceutical SEO', 'Research Institute SEO', 'Healthcare Technology SEO']
    },
    {
      industry: 'Aerospace & Defense',
      description: 'Specialized marketing for Karnataka\'s aerospace and defense manufacturing industry',
      icon: Crown,
      features: ['Aerospace Manufacturing SEO', 'Defense Contractor SEO', 'Aviation Services SEO', 'Space Technology SEO']
    },
    {
      industry: 'Tourism & Hospitality',
      description: 'Advanced marketing for Karnataka\'s tourism destinations and hospitality sector',
      icon: Target,
      features: ['Tourism Destination SEO', 'Hotel & Resort SEO', 'Travel Agency SEO', 'Heritage Tourism SEO']
    }
  ];

  const caseStudies = [
    {
      client: 'Leading IT Services Company',
      city: 'Bangalore',
      industry: 'Information Technology',
      challenge: 'IT company needed to establish global presence and compete with international tech giants',
      result: '4,800% international client growth',
      metrics: ['3,500+ IT keywords in top 3', '₹480Cr+ IT services revenue', '820% increase in global contracts']
    },
    {
      client: 'Biotech Research Company',
      city: 'Bangalore',
      industry: 'Biotechnology',
      challenge: 'Biotech firm needed to attract research partnerships and pharmaceutical collaborations',
      result: '4,200% research collaboration increase',
      metrics: ['2,200+ biotech keywords ranking', '₹285Cr+ research revenue', '680% increase in partnerships']
    },
    {
      client: 'Tourism Board Initiative',
      city: 'Mysore',
      industry: 'Tourism & Hospitality',
      challenge: 'Tourism organization needed to promote Karnataka destinations to domestic and international visitors',
      result: '3,900% tourism inquiry growth',
      metrics: ['1,800+ tourism keywords in top 5', '₹165Cr+ tourism revenue', '520% increase in visitor bookings']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-indigo-500/20 border border-indigo-500/30 rounded-full px-6 py-2 mb-8">
                    <MapPin className="w-4 h-4 text-indigo-400" />
                    <span className="text-indigo-400 font-medium">Karnataka Digital Marketing • Silicon Valley of India Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Digital Marketing Company in
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Karnataka</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier digital marketing services in Karnataka offering comprehensive SEO, PPC, social media marketing, and web development solutions for IT companies, biotech firms, aerospace manufacturers, and tourism businesses. Our Karnataka digital marketing agency provides professional services across Bangalore, Mysore, Mangalore, and Hubli with IT services SEO, biotech SEO, aerospace SEO, and tourism SEO. Serving 3,200+ Karnataka businesses with proven ₹1,150Cr+ revenue generation and 4,200% average business growth through strategic digital marketing and technology sector expertise in India's Silicon Valley state.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Karnataka Business Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-indigo-500 text-indigo-400 hover:bg-indigo-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Karnataka Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-indigo-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Karnataka Digital Marketing
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable digital marketing results from businesses across Karnataka's major technology and industrial cities.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-indigo-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Karnataka Major Cities We
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Serve</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Comprehensive digital marketing services across Karnataka's key technology and industrial centers.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {karnatakaCities.map((city, index) => (
                    <Card key={index} className="bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-4">{city.city}</h3>
                        <p className="text-slate-300 mb-6">{city.description}</p>
                        <div className="grid grid-cols-2 gap-4 mb-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-indigo-400">{city.businesses}</div>
                            <div className="text-slate-400 text-sm">Businesses Served</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-400">{city.growth}</div>
                            <div className="text-slate-400 text-sm">Average Growth</div>
                          </div>
                        </div>
                        <ul className="space-y-2">
                          {city.specializations.map((spec, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-indigo-400 mr-2 flex-shrink-0" />
                              {spec}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Karnataka Industry-Specific
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Digital Solutions</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized digital marketing strategies tailored for Karnataka's key industries and technology sectors.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {karnatakaIndustries.map((industry, index) => (
                    <Card key={index} className="bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mb-6">
                          <industry.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{industry.industry}</h3>
                        <p className="text-slate-300 mb-6">{industry.description}</p>
                        <ul className="space-y-2">
                          {industry.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-indigo-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Karnataka Digital Marketing Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-indigo-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-1">{study.city} • {study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-indigo-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Services by City */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Digital Marketing Services Across Karnataka
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <Link to="/services/seo/bangalore" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">SEO Services Bangalore</h4>
                    <p className="text-slate-400 text-sm">Search engine optimization for Bangalore IT companies</p>
                  </Link>
                  <Link to="/services/ppc/mysore" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">Google Ads Mysore</h4>
                    <p className="text-slate-400 text-sm">PPC advertising for Mysore tourism and businesses</p>
                  </Link>
                  <Link to="/services/social-media/mangalore" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">Social Media Mangalore</h4>
                    <p className="text-slate-400 text-sm">Social media marketing for Mangalore port businesses</p>
                  </Link>
                  <Link to="/services/web-development/hubli" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">Web Development Hubli</h4>
                    <p className="text-slate-400 text-sm">Website development for Hubli manufacturing companies</p>
                  </Link>
                  <Link to="/services/content/belgaum" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">Content Marketing Belgaum</h4>
                    <p className="text-slate-400 text-sm">Content creation for Belgaum businesses</p>
                  </Link>
                  <Link to="/services/email/gulbarga" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">Email Marketing Gulbarga</h4>
                    <p className="text-slate-400 text-sm">Email campaigns for Gulbarga agricultural businesses</p>
                  </Link>
                  <Link to="/industries/biotechnology" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">Biotech Industry SEO</h4>
                    <p className="text-slate-400 text-sm">Specialized SEO for Karnataka's biotech industry</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-indigo-400 font-semibold mb-2 group-hover:text-indigo-300">Karnataka Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our Karnataka clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-indigo-600 to-indigo-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Karnataka's Tech Markets?</h2>
                <p className="text-xl mb-8">
                  Join 3,200+ successful Karnataka businesses that trust GOD Digital Marketing for technology excellence. Proven strategies delivering 4,200% growth and ₹1,150Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-indigo-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Karnataka Business Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-indigo-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Karnataka Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="location" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Karnataka;
