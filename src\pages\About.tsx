
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Award, Users, TrendingUp, Globe, CheckCircle, Star, Calendar, MapPin, Mail, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import { Helmet } from 'react-helmet-async';

const About = () => {
  const stats = [
    { number: '7', label: 'Years International Experience', description: 'Across UK, US, Dubai, India, Kuwait, South Africa' },
    { number: '10M+', label: 'Monthly Views Generated', description: 'Built websites from zero to millions of views' },
    { number: '6', label: 'Countries Dominated', description: 'Proven SEO success across global markets' },
    { number: '₹150K', label: 'Premium Monthly Retainers', description: 'Elite businesses invest in international dominance' }
  ];

  const founder = {
    name: '<PERSON><PERSON>',
    role: 'Founder & International SEO Specialist',
    expertise: 'International SEO, AI-Powered Content Creation, Multi-Country Digital Marketing',
    credentials: '7 Years International Experience, Advanced SEO Tools Mastery, AI Automation Expert',
    description: 'International SEO consultant who has built websites from zero to 10+ million monthly views across 6 countries. Unlike local agencies that only know Indian SEO, I understand how search works across different countries, cultures, and markets.',
    achievements: [
      'Built Easy Outdoor (UK) to dominate car oven and Opus camper keywords',
      'Scaled Bulkland (US) across multiple US states with logistics SEO',
      'Established Shakespeare Flowers as Dubai market leader',
      'Generated 10+ million monthly views from zero traffic',
      'Mastered AI-powered content creation at scale (50-100 pieces monthly)',
      'Developed multi-country SEO strategies for international expansion'
    ],
    journey: [
      {
        country: 'India',
        year: '2017-2018',
        achievement: 'Started SEO journey, mastered local market dynamics and Hindi/English optimization'
      },
      {
        country: 'United Kingdom',
        year: '2018-2020',
        achievement: 'Dominated UK market with Easy Outdoor - car ovens and Opus campers to millions of views'
      },
      {
        country: 'United States',
        year: '2020-2021',
        achievement: 'Conquered US logistics market with Bulkland across 15+ states'
      },
      {
        country: 'Dubai (UAE)',
        year: '2021-2022',
        achievement: 'Established Shakespeare Flowers as Dubai flower delivery market leader'
      },
      {
        country: 'Kuwait',
        year: '2022-2023',
        achievement: 'Expanded real estate and business services across Kuwait market'
      },
      {
        country: 'South Africa',
        year: '2023-2024',
        achievement: 'Built international property investment presence across South African markets'
      }
    ]
  };

  const values = [
    {
      title: 'International Market Expertise',
      description: 'Unlike local agencies that only know Indian SEO, I understand how search works across different countries, cultures, and markets. Real experience across 6 countries.',
      icon: Globe
    },
    {
      title: 'Premium Results Focus',
      description: 'I work with serious businesses ready to invest ₹60K-150K monthly for international dominance. Quality over quantity, results over promises.',
      icon: TrendingUp
    },
    {
      title: 'AI-Powered Scaling',
      description: 'Advanced AI automation for content creation at scale (50-100 pieces monthly) while maintaining quality and relevance for each market.',
      icon: Star
    },
    {
      title: 'Proven Track Record',
      description: '7 years of building websites from zero to 10+ million monthly views. Real businesses, real results, real international success stories.',
      icon: Award
    }
  ];

  const expertise = [
    'International SEO (6 Countries)',
    'AI Content Creation at Scale',
    'Multi-Country Keyword Research',
    'Cross-Continental Link Building',
    'Advanced SEO Tools Mastery',
    'Export Business SEO Strategies',
    'Real Estate Lead Generation',
    'Social Media Automation'
  ];

  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <Globe className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Leading Digital Marketing Agency • 7 Years • 6 Countries</span>
              </div>

              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                About GOD Digital Marketing: Leading
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Digital Marketing Agency</span>
              </h1>

              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                GOD Digital Marketing stands as India's premier digital marketing agency, delivering comprehensive digital marketing services including SEO services, Google Ads management, social media marketing, content marketing, email marketing, and web development. Our digital marketing expertise spans 7 years across UK, US, Dubai, India, Kuwait & South Africa, generating 10+ million monthly views for clients. We provide full-service digital marketing solutions with proven results in search engine optimization, pay-per-click advertising, social media management, and online marketing strategies.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Digital Marketing Quote</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">See Digital Marketing Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-amber-400 mb-2">{stat.number}</div>
                  <div className="text-xl font-semibold text-white mb-2">{stat.label}</div>
                  <div className="text-slate-300">{stat.description}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Founder Profile Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                The International SEO
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Specialist Behind Your Success</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                7 years of hands-on international SEO experience across 6 countries. Real results, real businesses, real international market dominance.
              </p>
            </div>

            <div className="max-w-4xl mx-auto">
              <Card className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                <CardContent className="p-8">
                  <div className="flex items-start space-x-6">
                    <div className="w-24 h-24 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-3xl font-bold text-white">NT</span>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-3xl font-bold text-white mb-2">{founder.name}</h3>
                      <div className="text-amber-400 font-semibold mb-4 text-lg">{founder.role}</div>
                      <div className="text-slate-300 mb-6 leading-relaxed text-lg">{founder.description}</div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                          <h4 className="text-white font-semibold mb-3">Core Expertise:</h4>
                          <p className="text-slate-400">{founder.expertise}</p>
                        </div>

                        <div>
                          <h4 className="text-white font-semibold mb-3">Credentials:</h4>
                          <p className="text-slate-400">{founder.credentials}</p>
                        </div>
                      </div>

                      <div className="mb-6">
                        <h4 className="text-white font-semibold mb-3">Key Achievements:</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {founder.achievements.map((achievement, idx) => (
                            <div key={idx} className="flex items-start text-slate-400 text-sm">
                              <CheckCircle className="w-4 h-4 text-amber-400 mr-2 mt-0.5 flex-shrink-0" />
                              {achievement}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* International Journey Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                7-Year International
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> SEO Journey</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                From India to South Africa, each country taught me unique search behaviors, cultural nuances, and market dynamics that local agencies simply don't understand.
              </p>
            </div>

            <div className="max-w-4xl mx-auto">
              <div className="space-y-6">
                {founder.journey.map((step, index) => (
                  <Card key={index} className="bg-slate-900/80 border-slate-700 hover:border-amber-500/40 transition-all duration-300">
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Globe className="w-6 h-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2">
                            <h3 className="text-xl font-bold text-white">{step.country}</h3>
                            <span className="text-amber-400 font-semibold">{step.year}</span>
                          </div>
                          <p className="text-slate-300">{step.achievement}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                What Makes Nitin
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Different</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                7 years of international experience has taught me what local agencies simply don't understand about global SEO and market dynamics.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {values.map((value, index) => (
                <Card key={index} className="bg-slate-900/80 border-slate-700 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <value.icon className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-white mb-3">{value.title}</h3>
                        <p className="text-slate-300 leading-relaxed">{value.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Expertise Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Core
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Expertise Areas</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized skills developed through 7 years of hands-on international SEO experience across diverse markets and industries.
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
              {expertise.map((skill, index) => (
                <div key={index} className="bg-slate-900/60 border border-slate-700 rounded-lg p-4 text-center hover:border-amber-500/40 transition-colors">
                  <Award className="w-8 h-8 text-amber-400 mx-auto mb-2" />
                  <span className="text-white font-medium text-sm">{skill}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Business Info Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <div>
                <h2 className="text-4xl font-bold mb-6">
                  <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent">GOD Digital</span> Marketing
                </h2>
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <Calendar className="w-6 h-6 text-amber-400 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-white font-semibold mb-1">Experience</h3>
                      <p className="text-slate-300">2017-2024 - 7 years of international SEO expertise</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <Globe className="w-6 h-6 text-amber-400 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-white font-semibold mb-1">Global Markets</h3>
                      <p className="text-slate-300">UK, US, Dubai, India, Kuwait, South Africa</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <Users className="w-6 h-6 text-amber-400 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-white font-semibold mb-1">Client Focus</h3>
                      <p className="text-slate-300">Premium businesses investing ₹60K-150K monthly</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <TrendingUp className="w-6 h-6 text-amber-400 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-white font-semibold mb-1">Specialization</h3>
                      <p className="text-slate-300">International SEO & AI-powered automation</p>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h2 className="text-4xl font-bold mb-6">
                  <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent">My</span> Mission
                </h2>
                <div className="bg-slate-900/60 border border-slate-700 rounded-lg p-8">
                  <p className="text-slate-300 text-lg leading-relaxed mb-6">
                    "To help serious businesses dominate international markets through proven SEO strategies that I've personally tested and refined across 6 countries over 7 years."
                  </p>
                  <p className="text-slate-400 leading-relaxed">
                    Unlike local agencies that only know Indian SEO, I understand the nuances of international search behaviors, cultural differences, and market dynamics. I work exclusively with businesses ready to invest ₹60K-150K monthly for real international dominance, not quick fixes.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Work with an International SEO Specialist?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join the elite businesses investing ₹60K-150K monthly for international market dominance.
              7 years of proven results across UK, US, Dubai, India, Kuwait & South Africa.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get International SEO Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Book Premium Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default About;
