import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Code, TrendingUp, Users, CheckCircle, Building, Star, Crown, Monitor, Smartphone } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const WebDevelopmentDelhi = () => {
  const webServices = [
    'Website Development Delhi',
    'E-commerce Development Delhi',
    'Custom Web Application Delhi',
    'WordPress Development Delhi',
    'Shopify Development Delhi',
    'React Development Delhi',
    'Angular Development Delhi',
    'Node.js Development Delhi',
    'PHP Development Delhi',
    'Laravel Development Delhi',
    'Mobile App Development Delhi',
    'Progressive Web Apps Delhi',
    'API Development Delhi',
    'Database Development Delhi',
    'CMS Development Delhi',
    'Landing Page Development Delhi',
    'Website Redesign Delhi',
    'Website Maintenance Delhi',
    'Website Speed Optimization Delhi',
    'Website Security Services Delhi'
  ];

  const delhiAreas = [
    'Central Delhi Web Development',
    'South Delhi Web Services',
    'North Delhi Web Development',
    'East Delhi Web Services',
    'West Delhi Web Development',
    'New Delhi Web Services',
    'Connaught Place Web Development',
    'Karol Bagh Web Services',
    'Lajpat Nagar Web Development',
    'Khan Market Web Services',
    'Nehru Place Web Development',
    'Saket Web Services'
  ];

  const webTypes = [
    {
      type: 'Business Websites',
      description: 'Professional business websites that establish credibility and drive growth',
      icon: Building,
      features: ['Corporate Websites', 'Service Portfolios', 'Company Profiles', 'Professional Design']
    },
    {
      type: 'E-commerce Platforms',
      description: 'Powerful online stores that drive sales and revenue',
      icon: Star,
      features: ['Online Stores', 'Payment Integration', 'Inventory Management', 'Order Processing']
    },
    {
      type: 'Custom Web Applications',
      description: 'Tailored web applications for specific business needs',
      icon: Code,
      features: ['Custom Functionality', 'Database Integration', 'User Management', 'API Development']
    },
    {
      type: 'Mobile-First Design',
      description: 'Responsive websites optimized for mobile devices',
      icon: Smartphone,
      features: ['Mobile Optimization', 'Touch-Friendly Design', 'Fast Loading', 'Cross-Device Compatibility']
    }
  ];

  const webPackages = [
    {
      name: 'Website Development Delhi Starter',
      price: '₹35,000',
      period: 'one-time',
      description: 'Perfect for small Delhi businesses needing a professional website',
      features: [
        '5-Page Business Website',
        'Mobile-Responsive Design',
        'Basic SEO Setup',
        'Contact Form Integration',
        '1 Year Free Hosting',
        '3 Months Free Maintenance'
      ]
    },
    {
      name: 'Delhi Web Development Professional',
      price: '₹75,000',
      period: 'one-time',
      description: 'Comprehensive web development for growing Delhi businesses',
      features: [
        '10-Page Business Website',
        'Custom Design & Development',
        'Advanced SEO Integration',
        'CMS Integration',
        'E-commerce Functionality',
        'Analytics Setup',
        '6 Months Free Maintenance'
      ],
      popular: true
    },
    {
      name: 'Enterprise Web Development Delhi',
      price: '₹1,50,000',
      period: 'one-time',
      description: 'Advanced web development for large Delhi enterprises',
      features: [
        'Custom Web Application',
        'Advanced Functionality',
        'Database Integration',
        'API Development',
        'Security Implementation',
        'Dedicated Project Manager',
        '1 Year Free Maintenance'
      ]
    }
  ];

  const stats = [
    {
      metric: '500+',
      description: 'Websites Developed',
      detail: 'For Delhi businesses'
    },
    {
      metric: '99.9%',
      description: 'Uptime Guarantee',
      detail: 'Reliable hosting'
    },
    {
      metric: '₹25Cr+',
      description: 'Revenue Generated',
      detail: 'Through our websites'
    },
    {
      metric: '3 Sec',
      description: 'Average Load Time',
      detail: 'Fast performance'
    }
  ];

  const achievements = [
    'Top Web Development Company in Delhi',
    'WordPress Certified Developers',
    'Shopify Expert Partners',
    'React Development Specialists',
    'Mobile-First Design Leaders',
    'E-commerce Development Champions'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-5xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-emerald-500/20 border border-emerald-500/30 rounded-full px-6 py-2 mb-8">
                <Code className="w-4 h-4 text-emerald-400" />
                <span className="text-emerald-400 font-medium">Web Development Delhi • Digital Innovation Excellence</span>
              </div>
              
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                #1 Web Development Company in
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Delhi NCR</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto">
                Premier web development in Delhi offering comprehensive website development and custom web application services. Serving 500+ Delhi businesses across all districts - from government sector enterprises in Central Delhi to tech startups in Gurgaon. Expert web development solutions with proven ₹25Cr+ revenue generation and 99.9% uptime guarantee for Delhi clients.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Delhi Website Quote</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Web Developers: +91-9999-XXXX-XX</Link>
                </Button>
              </div>

              {/* Achievements */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                    <CheckCircle className="w-4 h-4 text-emerald-400 flex-shrink-0" />
                    <span className="text-slate-300">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Delhi Web Development
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Success Metrics</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12">
                Measurable web development results from Delhi businesses across all industries - delivering exceptional performance and revenue growth.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-emerald-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Web Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Website Types We
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Develop</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive web development services covering every type of website for Delhi businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-20">
              {webTypes.map((web, index) => (
                <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center mb-6">
                      <web.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{web.type}</h3>
                    <p className="text-slate-300 mb-6">{web.description}</p>
                    <ul className="space-y-2">
                      {web.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-emerald-400 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Complete Web Development Services
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Available in Delhi</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive range of web development services covering every aspect of website creation for Delhi businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {webServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Code className="w-8 h-8 text-emerald-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Delhi Web Development
                <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 bg-clip-text text-transparent"> Pricing</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Transparent web development pricing designed for Delhi businesses of all sizes.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {webPackages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-emerald-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-emerald-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-emerald-400">{pkg.price}</span>
                      <span className="text-slate-400 ml-2">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-emerald-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-emerald-600 to-emerald-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">Ready to Launch Your Delhi Website?</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto">
              Join 500+ Delhi businesses that trust GOD Digital Marketing for web development success. Proven solutions that deliver 99.9% uptime and ₹25Cr+ revenue generation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-emerald-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Website Quote</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-emerald-600 text-lg px-8 py-4">
                <Link to="/contact">Call Web Developers: +91-9999-XXXX-XX</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default WebDevelopmentDelhi;
