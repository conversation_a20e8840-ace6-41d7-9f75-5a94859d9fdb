import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Share2, TrendingUp, Users, CheckCircle, Building, Star, Crown, Heart, MessageCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const SocialMediaBangalore = () => {
  const socialMediaServices = [
    'Social Media Marketing Bangalore',
    'Social Media Management Bangalore',
    'Facebook Marketing Bangalore',
    'Instagram Marketing Bangalore',
    'LinkedIn Marketing Bangalore',
    'Twitter Marketing Bangalore',
    'YouTube Marketing Bangalore',
    'Social Media Automation Bangalore',
    'Influencer Marketing Bangalore',
    'Community Management Bangalore',
    'Social Media Advertising Bangalore',
    'Content Creation Bangalore',
    'Social Media Strategy Bangalore',
    'Brand Management Bangalore',
    'Social Media Analytics Bangalore',
    'Reputation Management Bangalore',
    'Tech Company Social Media Bangalore',
    'Startup Social Media Bangalore',
    'B2B Social Media Bangalore',
    'IT Services Social Media Bangalore'
  ];

  const bangaloreAreas = [
    'Koramangala Social Media',
    'Indiranagar Social Marketing',
    'Whitefield Social Media',
    'Electronic City Social Marketing',
    'HSR Layout Social Media',
    'BTM Layout Social Marketing',
    'Marathahalli Social Media',
    'Jayanagar Social Marketing',
    'Rajajinagar Social Media',
    'Malleshwaram Social Marketing',
    'Banashankari Social Media',
    'JP Nagar Social Marketing'
  ];

  const platforms = [
    {
      name: 'LinkedIn B2B Marketing',
      description: 'Professional networking and lead generation for Bangalore\'s tech community',
      icon: Users,
      features: ['Tech Recruitment', 'B2B Lead Generation', 'Thought Leadership', 'Professional Networking']
    },
    {
      name: 'Twitter Tech Engagement',
      description: 'Real-time engagement and thought leadership for tech companies',
      icon: MessageCircle,
      features: ['Tech Discussions', 'Industry Updates', 'Developer Community', 'Product Launches']
    },
    {
      name: 'Instagram Startup Branding',
      description: 'Visual storytelling and brand building for Bangalore startups',
      icon: Heart,
      features: ['Startup Culture', 'Behind-the-Scenes', 'Team Stories', 'Product Showcases']
    },
    {
      name: 'YouTube Tech Content',
      description: 'Video content strategy for tech companies and educational content',
      icon: Share2,
      features: ['Tech Tutorials', 'Product Demos', 'Company Culture', 'Educational Content']
    }
  ];

  const socialMediaPackages = [
    {
      name: 'Social Media Bangalore Starter',
      price: '₹20,000',
      period: '/month',
      description: 'Perfect for small Bangalore businesses starting with social media',
      features: [
        '2 Social Media Platforms',
        '15 Posts per Month',
        'Basic Graphics Design',
        'Community Management',
        'Monthly Analytics Report',
        'Bangalore Tech Targeting'
      ]
    },
    {
      name: 'Bangalore Social Media Professional',
      price: '₹35,000',
      period: '/month',
      description: 'Comprehensive social media management for growing Bangalore businesses',
      features: [
        '4 Social Media Platforms',
        '30 Posts per Month',
        'Professional Content Creation',
        'Paid Social Advertising',
        'Tech Influencer Partnerships',
        'Advanced Analytics',
        'Crisis Management'
      ],
      popular: true
    },
    {
      name: 'Enterprise Social Media Bangalore',
      price: '₹60,000',
      period: '/month',
      description: 'Advanced social media strategy for large Bangalore enterprises',
      features: [
        'All Major Platforms',
        'Unlimited Content Creation',
        'Video Content Production',
        'Advanced B2B Automation',
        'Custom Strategy Development',
        'Dedicated Social Media Manager',
        'Priority Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '1000+',
      description: 'Bangalore Social Media Accounts',
      detail: 'Successfully managed'
    },
    {
      metric: '260%',
      description: 'Average Engagement Increase',
      detail: 'For Bangalore businesses'
    },
    {
      metric: '₹16Cr+',
      description: 'Revenue Generated',
      detail: 'Through social media'
    },
    {
      metric: '94%',
      description: 'Client Satisfaction Rate',
      detail: 'Consistent quality delivery'
    }
  ];

  const achievements = [
    'Top Social Media Agency in Bangalore',
    'Tech Industry Specialists',
    'Startup Social Media Experts',
    'B2B Social Media Leaders',
    'LinkedIn Marketing Champions',
    'Developer Community Builders'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-5xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-pink-500/20 border border-pink-500/30 rounded-full px-6 py-2 mb-8">
                <Share2 className="w-4 h-4 text-pink-400" />
                <span className="text-pink-400 font-medium">Social Media Marketing Bangalore • Silicon Valley Tech Engagement</span>
              </div>
              
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                #1 Social Media Marketing Company in
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Bangalore</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto">
                Premier social media marketing in Bangalore offering comprehensive social media management across all platforms. Serving 1000+ Bangalore businesses across all areas - from IT services in Whitefield to startups in Koramangala. Expert social media solutions with proven ₹16Cr+ revenue generation and 260% average engagement increase for Bangalore clients in India's Silicon Valley.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Bangalore Social Media Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-pink-500 text-pink-400 hover:bg-pink-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Social Media Experts: +91-9999-XXXX-XX</Link>
                </Button>
              </div>

              {/* Achievements */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                    <CheckCircle className="w-4 h-4 text-pink-400 flex-shrink-0" />
                    <span className="text-slate-300">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Bangalore Social Media Marketing
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Success Metrics</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12">
                Measurable social media results from Bangalore businesses across all industries - delivering exceptional engagement in India's tech capital.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-pink-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Platforms Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Social Media Platforms We
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Master</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Specialized social media management for Bangalore's tech, startup, and innovation landscape.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-20">
              {platforms.map((platform, index) => (
                <Card key={index} className="bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg flex items-center justify-center mb-6">
                      <platform.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{platform.name}</h3>
                    <p className="text-slate-300 mb-6">{platform.description}</p>
                    <ul className="space-y-2">
                      {platform.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-pink-400 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Complete Social Media Services
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Available in Bangalore</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive range of social media services covering every aspect of digital engagement for Bangalore businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {socialMediaServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Share2 className="w-8 h-8 text-pink-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-pink-500 text-pink-400 hover:bg-pink-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Bangalore Social Media Marketing
                <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Pricing</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Competitive social media marketing pricing designed for Bangalore's tech-savvy business environment.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {socialMediaPackages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-pink-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-pink-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-pink-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-pink-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-pink-600 to-pink-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">Ready to Dominate Bangalore Social Media?</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto">
              Join 1000+ Bangalore businesses that trust GOD Digital Marketing for social media success. Proven strategies that deliver 260% engagement increase and ₹16Cr+ revenue generation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-pink-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Social Media Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-pink-600 text-lg px-8 py-4">
                <Link to="/contact">Call Social Media Experts: +91-9999-XXXX-XX</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default SocialMediaBangalore;
