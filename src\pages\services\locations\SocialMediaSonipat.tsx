import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Share2, TrendingUp, Users, CheckCircle, Building, Star, Factory, Heart, MessageCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const SocialMediaSonipat = () => {
  const socialMediaServices = [
    'Social Media Marketing Sonipat',
    'Social Media Management Sonipat',
    'Facebook Marketing Sonipat',
    'Instagram Marketing Sonipat',
    'LinkedIn B2B Marketing Sonipat',
    'YouTube Marketing Sonipat',
    'Industrial Social Media Sonipat',
    'B2B Social Media Strategy Sonipat',
    'Manufacturing Social Media Sonipat',
    'Automobile Industry Social Media Sonipat',
    'Textile Industry Social Media Sonipat',
    'Local Business Social Media Sonipat',
    'Community Management Sonipat',
    'Social Media Advertising Sonipat',
    'Content Creation Sonipat',
    'Brand Management Sonipat',
    'Social Media Analytics Sonipat',
    'Reputation Management Sonipat',
    'Crisis Management Sonipat',
    'Social Media Training Sonipat'
  ];

  const sonipatAreas = [
    'Sector 14 Social Media',
    'Model Town Social Marketing',
    'Rai Social Media',
    'Gohana Social Marketing',
    'Kharkhoda Social Media',
    'Mundlana Social Marketing',
    'Kathura Social Media',
    'Gannaur Social Marketing',
    'Industrial Area Social Media',
    'Sonipat City Social Marketing',
    'Delhi Road Social Media',
    'GT Road Social Marketing'
  ];

  const platforms = [
    {
      name: 'LinkedIn B2B Marketing',
      description: 'Professional networking and B2B lead generation for Sonipat industries',
      icon: Users,
      features: ['Company Pages', 'B2B Lead Generation', 'Industry Networking', 'Thought Leadership']
    },
    {
      name: 'Facebook Business Marketing',
      description: 'Local business promotion and community building for Sonipat',
      icon: Share2,
      features: ['Business Pages', 'Local Advertising', 'Community Building', 'Customer Service']
    },
    {
      name: 'YouTube Industrial Content',
      description: 'Video marketing for manufacturing and industrial businesses',
      icon: MessageCircle,
      features: ['Product Demonstrations', 'Industrial Videos', 'Training Content', 'Company Showcases']
    },
    {
      name: 'Instagram Visual Marketing',
      description: 'Visual storytelling for Sonipat businesses and products',
      icon: Heart,
      features: ['Product Photography', 'Behind-the-Scenes', 'Local Stories', 'Brand Building']
    }
  ];

  const socialMediaPackages = [
    {
      name: 'Social Media Sonipat Starter',
      price: '₹12,000',
      period: '/month',
      description: 'Perfect for small Sonipat businesses starting with social media',
      features: [
        '2 Social Media Platforms',
        '12 Posts per Month',
        'Basic Graphics Design',
        'Community Management',
        'Monthly Analytics Report',
        'Local Sonipat Targeting'
      ]
    },
    {
      name: 'Sonipat Social Media Professional',
      price: '₹20,000',
      period: '/month',
      description: 'Comprehensive social media management for growing Sonipat businesses',
      features: [
        '4 Social Media Platforms',
        '24 Posts per Month',
        'Professional Content Creation',
        'B2B Social Advertising',
        'Industrial Content Strategy',
        'Advanced Analytics',
        'Crisis Management'
      ],
      popular: true
    },
    {
      name: 'Enterprise Social Media Sonipat',
      price: '₹35,000',
      period: '/month',
      description: 'Advanced social media strategy for large Sonipat manufacturers',
      features: [
        'All Major Platforms',
        'Unlimited Content Creation',
        'Video Content Production',
        'B2B Automation',
        'Custom Industrial Strategy',
        'Dedicated Social Media Manager',
        'Priority Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '180+',
      description: 'Sonipat Social Media Accounts',
      detail: 'Successfully managed'
    },
    {
      metric: '220%',
      description: 'Average Engagement Increase',
      detail: 'For Sonipat businesses'
    },
    {
      metric: '₹4Cr+',
      description: 'Revenue Generated',
      detail: 'Through social media'
    },
    {
      metric: '92%',
      description: 'Client Satisfaction Rate',
      detail: 'Consistent quality delivery'
    }
  ];

  const achievements = [
    'Top Social Media Agency in Sonipat',
    'B2B Social Media Specialists',
    'Industrial Content Experts',
    'Manufacturing Marketing Leaders',
    'Automobile Industry Partners',
    'Local Business Champions'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-5xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-purple-500/20 border border-purple-500/30 rounded-full px-6 py-2 mb-8">
                <Factory className="w-4 h-4 text-purple-400" />
                <span className="text-purple-400 font-medium">Social Media Marketing Sonipat • Industrial Gateway Engagement</span>
              </div>
              
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                #1 Social Media Marketing Company in
                <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Sonipat</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto">
                Premier social media marketing in Sonipat offering comprehensive B2B and industrial social media management. Serving 180+ Sonipat businesses across all sectors - from automobile manufacturers to textile industries in this industrial hub. Expert social media solutions with proven ₹4Cr+ revenue generation and 220% average engagement increase for Sonipat clients.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Sonipat Social Media Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Social Media Experts: +91-9999-XXXX-XX</Link>
                </Button>
              </div>

              {/* Achievements */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                    <CheckCircle className="w-4 h-4 text-purple-400 flex-shrink-0" />
                    <span className="text-slate-300">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Sonipat Social Media Marketing
                <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Success Metrics</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12">
                Measurable social media results from Sonipat businesses across all industries - delivering exceptional B2B engagement and industrial brand building.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-purple-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Platforms Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Social Media Platforms We
                <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Master</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Specialized social media management for Sonipat's industrial and manufacturing business landscape.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-20">
              {platforms.map((platform, index) => (
                <Card key={index} className="bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-6">
                      <platform.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{platform.name}</h3>
                    <p className="text-slate-300 mb-6">{platform.description}</p>
                    <ul className="space-y-2">
                      {platform.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-purple-400 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Complete Social Media Services
                <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Available in Sonipat</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive range of social media services covering every aspect of digital engagement for Sonipat industrial businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {socialMediaServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Share2 className="w-8 h-8 text-purple-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Sonipat Social Media Marketing
                <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Pricing</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Affordable social media marketing pricing designed for Sonipat businesses of all sizes.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {socialMediaPackages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-purple-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-purple-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-purple-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-purple-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-purple-600 to-purple-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">Ready to Dominate Sonipat Social Media?</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto">
              Join 180+ Sonipat businesses that trust GOD Digital Marketing for social media success. Proven strategies that deliver 220% engagement increase and ₹4Cr+ revenue generation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-purple-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Social Media Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 text-lg px-8 py-4">
                <Link to="/contact">Call Social Media Experts: +91-9999-XXXX-XX</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default SocialMediaSonipat;
