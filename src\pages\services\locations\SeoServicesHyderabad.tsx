import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SeoServicesHyderabad = () => {
  const seoServices = [
    'Local SEO Services Hyderabad',
    'International SEO Hyderabad',
    'E-commerce SEO Hyderabad',
    'Technical SEO Audit Hyderabad',
    'Enterprise SEO Solutions Hyderabad',
    'Mobile SEO Optimization Hyderabad',
    'Voice Search SEO Hyderabad',
    'Video SEO Services Hyderabad',
    'Image SEO Optimization Hyderabad',
    'Schema Markup Implementation Hyderabad',
    'Core Web Vitals Optimization Hyderabad',
    'Page Speed Optimization Hyderabad',
    'SEO Content Strategy Hyderabad',
    'Keyword Research & Analysis Hyderabad',
    'Competitor SEO Analysis Hyderabad',
    'SEO Link Building Hyderabad',
    'Local Citation Building Hyderabad',
    'Google My Business Optimization Hyderabad',
    'Multi-location SEO Hyderabad',
    'Pharma SEO Hyderabad'
  ];

  const hyderabadAreas = [
    'Hitech City SEO Services',
    'Gachibowli SEO Company',
    'Madhapur SEO Agency',
    'Kondapur SEO Services',
    'Jubilee Hills SEO Company',
    'Banjara Hills SEO Services',
    'Secunderabad SEO Agency',
    'Begumpet SEO Services',
    'Ameerpet SEO Company',
    'Kukatpally SEO Services',
    'Miyapur SEO Agency',
    'Uppal SEO Services',
    'LB Nagar SEO Company',
    'Dilsukhnagar SEO Services',
    'Mehdipatnam SEO Agency',
    'Tolichowki SEO Services'
  ];

  const industries = [
    'IT Services SEO Hyderabad',
    'Pharmaceutical SEO Hyderabad',
    'Biotechnology SEO Hyderabad',
    'Healthcare SEO Hyderabad',
    'Financial Services SEO Hyderabad',
    'Real Estate SEO Hyderabad',
    'Education SEO Hyderabad',
    'Manufacturing SEO Hyderabad',
    'Aerospace SEO Hyderabad',
    'Defense SEO Hyderabad',
    'E-commerce SEO Hyderabad',
    'Government SEO Hyderabad',
    'Hospitality SEO Hyderabad',
    'Food & Restaurant SEO Hyderabad',
    'Logistics SEO Hyderabad',
    'Startup SEO Hyderabad'
  ];

  const seoPackages = [
    {
      name: 'Local SEO Hyderabad Starter',
      price: '₹25,000',
      period: '/month',
      description: 'Perfect for small Hyderabad businesses targeting local customers',
      features: [
        'Google My Business Optimization',
        'Local Keyword Research (50 keywords)',
        'On-Page SEO (10 pages)',
        'Local Citation Building (25 citations)',
        'Monthly Local SEO Report',
        'Hyderabad Area Targeting'
      ]
    },
    {
      name: 'Hyderabad SEO Professional',
      price: '₹46,000',
      period: '/month',
      description: 'Comprehensive SEO for growing Hyderabad businesses',
      features: [
        'Everything in Starter',
        'Advanced Keyword Research (150 keywords)',
        'Technical SEO Audit & Fixes',
        'Content Marketing Strategy',
        'Link Building Campaign',
        'Competitor Analysis',
        'Multi-location SEO Setup'
      ],
      popular: true
    },
    {
      name: 'Enterprise SEO Hyderabad',
      price: '₹80,000',
      period: '/month',
      description: 'Advanced SEO for large Hyderabad enterprises',
      features: [
        'Everything in Professional',
        'Enterprise-level SEO Strategy',
        'Advanced Technical SEO',
        'International SEO Setup',
        'Custom SEO Dashboard',
        'Dedicated SEO Manager',
        'Priority Support'
      ]
    }
  ];

  const seoProcess = [
    {
      step: '1',
      title: 'Hyderabad Market SEO Audit',
      description: 'Comprehensive analysis of your website\'s SEO performance in Hyderabad\'s tech-driven market',
      icon: Search
    },
    {
      step: '2',
      title: 'Tech-Pharma Keyword Strategy',
      description: 'Strategic keyword research targeting Hyderabad\'s IT and pharmaceutical sectors',
      icon: Target
    },
    {
      step: '3',
      title: 'Technical SEO Implementation',
      description: 'Advanced technical SEO optimization leveraging Hyderabad\'s tech expertise',
      icon: Zap
    },
    {
      step: '4',
      title: 'Content & Link Building',
      description: 'High-quality content creation and authoritative link building for Hyderabad market dominance',
      icon: TrendingUp
    }
  ];

  const stats = [
    {
      metric: '1500+',
      description: 'Hyderabad Websites Optimized',
      detail: 'Across all industries'
    },
    {
      metric: '640%',
      description: 'Average Organic Traffic Increase',
      detail: 'For Hyderabad businesses'
    },
    {
      metric: '₹40Cr+',
      description: 'Revenue Generated Through SEO',
      detail: 'For Hyderabad clients'
    },
    {
      metric: '90%',
      description: 'First Page Rankings Achieved',
      detail: 'For target keywords'
    }
  ];

  const achievements = [
    'Google Premier Partner for Hyderabad SEO',
    'Top SEO Company in Hyderabad',
    'IT Services SEO Specialists',
    'Pharmaceutical SEO Experts',
    'Healthcare SEO Leaders',
    'International SEO Consultants'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-blue-500/20 border border-blue-500/30 rounded-full px-6 py-2 mb-8">
                    <Search className="w-4 h-4 text-blue-400" />
                    <span className="text-blue-400 font-medium">SEO Services Hyderabad • Cyberabad SEO Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 SEO Services Company in
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Hyderabad</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier SEO services in Hyderabad offering comprehensive search engine optimization solutions from local SEO to enterprise-level strategies. Serving 1500+ Hyderabad businesses across all areas - from IT companies in Hitech City to pharmaceutical companies in Genome Valley. Expert SEO solutions with proven ₹40Cr+ revenue generation and 640% average traffic increase for Hyderabad clients in India's Cyberabad.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Hyderabad SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Hyderabad SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-blue-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Hyderabad SEO Services
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable SEO results from Hyderabad businesses across all industries.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-blue-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* SEO Process Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Our Hyderabad SEO
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Process</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Proven 4-step SEO methodology specifically designed for Hyderabad's tech and pharmaceutical market dynamics.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
                  {seoProcess.map((process, index) => (
                    <Card key={index} className="bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                      <CardContent className="p-8 text-center">
                        <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                          <process.icon className="w-8 h-8 text-white" />
                        </div>
                        <div className="text-3xl font-bold text-blue-400 mb-4">Step {process.step}</div>
                        <h3 className="text-xl font-bold text-white mb-4">{process.title}</h3>
                        <p className="text-slate-300">{process.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Hyderabad SEO Services
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive SEO pricing packages designed for Hyderabad's tech and pharmaceutical business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {seoPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-blue-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-blue-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-blue-600 to-blue-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Hyderabad Search Results?</h2>
                <p className="text-xl mb-8">
                  Join 1500+ Hyderabad businesses that trust GOD Digital Marketing for SEO success. Proven strategies that deliver 640% traffic increase and ₹40Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Hyderabad SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4">
                    <Link to="/contact">Call SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="seo" currentLocation="hyderabad" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoServicesHyderabad;
