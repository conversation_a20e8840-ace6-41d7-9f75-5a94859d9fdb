
import React from 'react';
import { TrendingUp, Users, DollarSign, Globe } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

const ResultsSection = () => {
  const results = [
    {
      icon: TrendingUp,
      metric: '10M+',
      description: 'Monthly views generated from zero',
      case: 'Built multiple websites to 5-10 million monthly views'
    },
    {
      icon: Globe,
      metric: '6',
      description: 'Countries with proven SEO success',
      case: 'UK, US, Dubai, India, Kuwait, South Africa dominance'
    },
    {
      icon: Users,
      metric: '1000s',
      description: 'Monthly traffic increases achieved',
      case: 'Consistently scaled websites from zero to thousands monthly'
    },
    {
      icon: DollarSign,
      metric: '₹150K',
      description: 'Premium monthly retainers earned',
      case: 'Premium positioning with ₹60K-150K monthly clients'
    }
  ];

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            International SEO Results
            <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Across 6 Countries</span>
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            7 years of proven international SEO expertise. Real results from UK to Dubai, US to South Africa - building digital empires that dominate global markets.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {results.map((result, index) => (
            <Card key={index} className="bg-gradient-to-br from-slate-800 to-slate-900 border-amber-500/20 text-center group hover:scale-105 transition-transform duration-300">
              <CardContent className="p-8">
                <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:rotate-12 transition-transform">
                  <result.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-4xl font-bold text-amber-400 mb-3">{result.metric}</div>
                <p className="text-white font-semibold mb-2">{result.description}</p>
                <p className="text-slate-400 text-sm">{result.case}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ResultsSection;
