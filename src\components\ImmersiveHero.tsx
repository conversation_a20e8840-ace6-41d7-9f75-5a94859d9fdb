import React, { useRef, useMemo, useEffect, useState, Suspense } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Text, OrbitControls, Sphere, Box, Cylinder, Torus } from '@react-three/drei';
import { Mesh, Group, Vector3 } from 'three';
import { Button } from '@/components/ui/button';
import { ArrowRight, Play, Volume2, VolumeX } from 'lucide-react';
import { Link } from 'react-router-dom';

// Phase indicator component
const PhaseIndicator = ({ currentPhase, totalPhases }: { currentPhase: number; totalPhases: number }) => (
  <div className="fixed top-1/2 right-8 transform -translate-y-1/2 z-50">
    <div className="flex flex-col space-y-3">
      {Array.from({ length: totalPhases }, (_, i) => (
        <div
          key={i}
          className={`w-3 h-3 rounded-full border-2 transition-all duration-500 ${
            i <= currentPhase
              ? 'bg-amber-400 border-amber-400 shadow-lg shadow-amber-400/50'
              : 'border-white/30 bg-transparent'
          }`}
        />
      ))}
    </div>
    <div className="mt-4 text-xs text-white/70 text-center">
      {currentPhase + 1} / {totalPhases}
    </div>
  </div>
);

// Floating particles component
const FloatingParticles = ({ count = 30 }: { count?: number }) => {
  const meshRef = useRef<Group>(null);
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.children.forEach((child, i) => {
        child.position.y += Math.sin(state.clock.elapsedTime + i) * 0.001;
        child.rotation.y += 0.002;
      });
    }
  });

  return (
    <group ref={meshRef}>
      {Array.from({ length: count }, (_, i) => (
        <Sphere
          key={i}
          args={[0.02, 8, 8]}
          position={[
            (Math.random() - 0.5) * 20,
            (Math.random() - 0.5) * 10,
            (Math.random() - 0.5) * 20
          ]}
        >
          <meshStandardMaterial 
            color={i % 3 === 0 ? "#fbbf24" : i % 3 === 1 ? "#0ea5e9" : "#10b981"} 
            emissive={i % 3 === 0 ? "#fbbf24" : i % 3 === 1 ? "#0ea5e9" : "#10b981"}
            emissiveIntensity={0.2}
          />
        </Sphere>
      ))}
    </group>
  );
};

// Phase 1: Digital Discovery
const DiscoveryPhase = ({ active }: { active: boolean }) => {
  const groupRef = useRef<Group>(null);
  
  useFrame((state) => {
    if (groupRef.current && active) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.2;
    }
  });

  return (
    <group ref={groupRef} visible={active}>
      <Text
        fontSize={1.5}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        position={[0, 2, 0]}
      >
        DIGITAL DISCOVERY
      </Text>
      
      <Text
        fontSize={0.4}
        color="#fbbf24"
        anchorX="center"
        anchorY="middle"
        position={[0, 1, 0]}
      >
        We uncover your digital potential
      </Text>

      {/* Search radar visualization */}
      <group>
        {[1, 2, 3].map((radius, i) => (
          <Torus
            key={i}
            args={[radius, 0.02, 16, 100]}
            position={[0, 0, 0]}
            rotation={[Math.PI / 2, 0, 0]}
          >
            <meshStandardMaterial 
              color="#0ea5e9" 
              transparent 
              opacity={0.3 - i * 0.1}
              emissive="#0ea5e9"
              emissiveIntensity={0.1}
            />
          </Torus>
        ))}
      </group>

      {/* Floating data cubes */}
      {Array.from({ length: 8 }, (_, i) => (
        <Box
          key={i}
          args={[0.1, 0.1, 0.1]}
          position={[
            Math.cos((i / 8) * Math.PI * 2) * 2.5,
            Math.sin((i / 8) * Math.PI * 2) * 0.5,
            Math.sin((i / 8) * Math.PI * 2) * 2.5
          ]}
        >
          <meshStandardMaterial color="#10b981" />
        </Box>
      ))}
    </group>
  );
};

// Phase 2: Strategic Planning
const StrategyPhase = ({ active }: { active: boolean }) => {
  const groupRef = useRef<Group>(null);
  
  useFrame((state) => {
    if (groupRef.current && active) {
      groupRef.current.children.forEach((child, i) => {
        if (child.userData.piece) {
          child.position.y = Math.sin(state.clock.elapsedTime + i) * 0.1;
        }
      });
    }
  });

  return (
    <group ref={groupRef} visible={active}>
      <Text
        fontSize={1.5}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        position={[0, 3, 0]}
      >
        STRATEGIC PLANNING
      </Text>
      
      <Text
        fontSize={0.4}
        color="#fbbf24"
        anchorX="center"
        anchorY="middle"
        position={[0, 2.2, 0]}
      >
        Data-driven strategies that deliver results
      </Text>

      {/* Chess board base */}
      <Box args={[4, 0.1, 4]} position={[0, 0, 0]}>
        <meshStandardMaterial color="#1e293b" />
      </Box>

      {/* Strategic pieces */}
      {Array.from({ length: 6 }, (_, i) => (
        <Cylinder
          key={i}
          args={[0.15, 0.15, 0.5, 8]}
          position={[
            (i % 3 - 1) * 1.2,
            0.35,
            Math.floor(i / 3) * 1.2 - 0.6
          ]}
          userData={{ piece: true }}
        >
          <meshStandardMaterial 
            color={i % 2 === 0 ? "#fbbf24" : "#0ea5e9"}
            metalness={0.8}
            roughness={0.2}
          />
        </Cylinder>
      ))}
    </group>
  );
};

// Phase 3: Creative Development
const CreativePhase = ({ active }: { active: boolean }) => {
  const groupRef = useRef<Group>(null);
  
  useFrame((state) => {
    if (groupRef.current && active) {
      groupRef.current.rotation.y = state.clock.elapsedTime * 0.3;
    }
  });

  return (
    <group ref={groupRef} visible={active}>
      <Text
        fontSize={1.5}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        position={[0, 3, 0]}
      >
        CREATIVE DEVELOPMENT
      </Text>
      
      <Text
        fontSize={0.4}
        color="#fbbf24"
        anchorX="center"
        anchorY="middle"
        position={[0, 2.2, 0]}
      >
        Where ideas transform into digital realities
      </Text>

      {/* Morphing geometric shapes */}
      <group>
        <Sphere args={[0.5, 32, 32]} position={[-2, 0, 0]}>
          <meshStandardMaterial color="#8b5cf6" metalness={0.8} roughness={0.2} />
        </Sphere>
        
        <Box args={[1, 1, 1]} position={[0, 0, 0]}>
          <meshStandardMaterial color="#10b981" metalness={0.8} roughness={0.2} />
        </Box>
        
        <Cylinder args={[0.5, 0.5, 1, 8]} position={[2, 0, 0]}>
          <meshStandardMaterial color="#f59e0b" metalness={0.8} roughness={0.2} />
        </Cylinder>
      </group>

      {/* Connecting lines */}
      {Array.from({ length: 3 }, (_, i) => (
        <Box
          key={i}
          args={[1.5, 0.02, 0.02]}
          position={[i * 2 - 1, 0, 0]}
        >
          <meshStandardMaterial color="#ffffff" emissive="#ffffff" emissiveIntensity={0.2} />
        </Box>
      ))}
    </group>
  );
};

// Phase 4: Launch & Implementation
const LaunchPhase = ({ active }: { active: boolean }) => {
  const rocketRef = useRef<Group>(null);
  
  useFrame((state) => {
    if (rocketRef.current && active) {
      rocketRef.current.position.y = Math.sin(state.clock.elapsedTime * 2) * 0.2 + 1;
      rocketRef.current.rotation.z = Math.sin(state.clock.elapsedTime) * 0.1;
    }
  });

  return (
    <group visible={active}>
      <Text
        fontSize={1.5}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        position={[0, 4, 0]}
      >
        LAUNCH & IMPLEMENTATION
      </Text>
      
      <Text
        fontSize={0.4}
        color="#fbbf24"
        anchorX="center"
        anchorY="middle"
        position={[0, 3.2, 0]}
      >
        Your brand reaches new heights
      </Text>

      {/* Rocket */}
      <group ref={rocketRef}>
        <Cylinder args={[0.2, 0.4, 2, 8]} position={[0, 0, 0]}>
          <meshStandardMaterial color="#ef4444" metalness={0.8} roughness={0.2} />
        </Cylinder>
        <Cylinder args={[0.1, 0.2, 0.5, 8]} position={[0, 1.25, 0]}>
          <meshStandardMaterial color="#fbbf24" metalness={0.8} roughness={0.2} />
        </Cylinder>
      </group>

      {/* Orbiting satellites */}
      {Array.from({ length: 4 }, (_, i) => (
        <group key={i} rotation={[0, (i / 4) * Math.PI * 2, 0]}>
          <Box args={[0.15, 0.1, 0.1]} position={[3, 2, 0]}>
            <meshStandardMaterial color="#0ea5e9" />
          </Box>
        </group>
      ))}
    </group>
  );
};

// Phase 5: Growth & Analytics
const GrowthPhase = ({ active }: { active: boolean }) => {
  const chartsRef = useRef<Group>(null);
  
  useFrame((state) => {
    if (chartsRef.current && active) {
      chartsRef.current.children.forEach((child, i) => {
        child.scale.y = 1 + Math.sin(state.clock.elapsedTime + i) * 0.2;
      });
    }
  });

  return (
    <group visible={active}>
      <Text
        fontSize={1.5}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        position={[0, 3, 0]}
      >
        GROWTH & ANALYTICS
      </Text>
      
      <Text
        fontSize={0.4}
        color="#fbbf24"
        anchorX="center"
        anchorY="middle"
        position={[0, 2.2, 0]}
      >
        Measurable results, exponential growth
      </Text>

      {/* 3D Bar Charts */}
      <group ref={chartsRef}>
        {Array.from({ length: 7 }, (_, i) => (
          <Box
            key={i}
            args={[0.3, (i + 1) * 0.3, 0.3]}
            position={[(i - 3) * 0.8, ((i + 1) * 0.3) / 2, 0]}
          >
            <meshStandardMaterial 
              color={`hsl(${120 + i * 20}, 70%, 50%)`}
              metalness={0.6}
              roughness={0.3}
            />
          </Box>
        ))}
      </group>

      {/* Growth arrows */}
      {Array.from({ length: 3 }, (_, i) => (
        <group key={i} position={[i * 2 - 2, 2, 0]}>
          <Cylinder args={[0.05, 0.05, 1, 8]} rotation={[0, 0, Math.PI / 4]}>
            <meshStandardMaterial color="#10b981" />
          </Cylinder>
          <Cylinder args={[0.15, 0.02, 0.3, 8]} position={[0.5, 0.5, 0]} rotation={[0, 0, Math.PI / 4]}>
            <meshStandardMaterial color="#10b981" />
          </Cylinder>
        </group>
      ))}
    </group>
  );
};

// Main 3D Scene
const Scene = ({ currentPhase }: { currentPhase: number }) => {
  const { camera, gl } = useThree();
  
  useEffect(() => {
    // Set up camera for cinematic view
    camera.position.set(0, 2, 8);
    camera.lookAt(0, 0, 0);
  }, [camera]);

  return (
    <>
      {/* Lighting */}
      <ambientLight intensity={0.3} />
      <directionalLight position={[10, 10, 5]} intensity={1} />
      <pointLight position={[-10, -10, -5]} intensity={0.5} color="#fbbf24" />
      <spotLight 
        position={[0, 10, 0]} 
        intensity={2}
        angle={0.3}
        penumbra={0.5}
        color="#ffffff"
      />

      {/* Background particles */}
      <FloatingParticles count={window.innerWidth < 768 ? 15 : 30} />

      {/* Phase components */}
      <DiscoveryPhase active={currentPhase === 0} />
      <StrategyPhase active={currentPhase === 1} />
      <CreativePhase active={currentPhase === 2} />
      <LaunchPhase active={currentPhase === 3} />
      <GrowthPhase active={currentPhase === 4} />

      {/* Camera controls */}
      <OrbitControls 
        enablePan={false}
        enableZoom={false}
        enableRotate={true}
        autoRotate={false}
        maxPolarAngle={Math.PI / 2}
        minPolarAngle={Math.PI / 4}
      />
    </>
  );
};

// Error Boundary for 3D content
class ThreeErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }
    return this.props.children;
  }
}

// Loading fallback
const SceneFallback = () => (
  <div className="w-full h-full flex items-center justify-center bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900">
    <div className="text-center">
      <div className="w-16 h-16 border-4 border-amber-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
      <div className="text-white text-xl font-semibold">Loading Digital Experience...</div>
    </div>
  </div>
);

// Main Immersive Hero Component
const ImmersiveHero = () => {
  const [currentPhase, setCurrentPhase] = useState(0);
  const [autoPlay, setAutoPlay] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(false);
  
  const phases = [
    'Digital Discovery',
    'Strategic Planning', 
    'Creative Development',
    'Launch & Implementation',
    'Growth & Analytics'
  ];

  useEffect(() => {
    if (!autoPlay) return;
    
    const interval = setInterval(() => {
      setCurrentPhase((prev) => (prev + 1) % phases.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [autoPlay, phases.length]);

  const canvasSettings = useMemo(() => ({
    camera: { 
      position: [0, 2, 8] as [number, number, number],
      fov: 50
    },
    gl: { 
      antialias: window.devicePixelRatio <= 2,
      alpha: true,
      powerPreference: "high-performance" as const,
      logarithmicDepthBuffer: true
    },
    dpr: Math.min(window.devicePixelRatio, 2),
    performance: {
      min: 0.2,
      max: 1.0,
      debounce: 200
    }
  }), []);

  return (
    <section className="relative h-screen w-full overflow-hidden bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900">
      {/* 3D Canvas */}
      <div className="absolute inset-0">
        <ThreeErrorBoundary 
          fallback={
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900">
              <div className="text-center">
                <div className="text-6xl font-bold text-white mb-4">GOD</div>
                <div className="text-xl text-amber-400 mb-4">DIGITAL MARKETING</div>
                <div className="text-slate-300">Experience the future of digital marketing</div>
              </div>
            </div>
          }
        >
          <Suspense fallback={<SceneFallback />}>
            <Canvas {...canvasSettings}>
              <Scene currentPhase={currentPhase} />
            </Canvas>
          </Suspense>
        </ThreeErrorBoundary>
      </div>

      {/* UI Overlay */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Phase Indicator */}
        <PhaseIndicator currentPhase={currentPhase} totalPhases={phases.length} />

        {/* Controls */}
        <div className="fixed top-8 right-8 flex space-x-4 pointer-events-auto">
          <button
            onClick={() => setAutoPlay(!autoPlay)}
            className="p-3 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 text-white hover:bg-white/20 transition-all"
            title={autoPlay ? "Pause" : "Play"}
          >
            {autoPlay ? <Play className="w-5 h-5" /> : <Play className="w-5 h-5" />}
          </button>
          
          <button
            onClick={() => setSoundEnabled(!soundEnabled)}
            className="p-3 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 text-white hover:bg-white/20 transition-all"
            title={soundEnabled ? "Mute" : "Unmute"}
          >
            {soundEnabled ? <Volume2 className="w-5 h-5" /> : <VolumeX className="w-5 h-5" />}
          </button>
        </div>

        {/* Current Phase Info */}
        <div className="fixed bottom-24 left-8 max-w-md pointer-events-auto">
          <div className="bg-black/50 backdrop-blur-sm rounded-xl p-6 border border-white/10">
            <h3 className="text-2xl font-bold text-white mb-2">
              {phases[currentPhase]}
            </h3>
            <p className="text-slate-300 mb-4">
              Experience the power of modern digital marketing through our proven 5-phase approach.
            </p>
            <div className="flex space-x-3">
              <Button asChild className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700">
                <Link to="/quote" className="flex items-center">
                  Start Your Journey
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Link>
              </Button>
              <Button asChild variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white">
                <Link to="/services">
                  Explore Services
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Phase navigation dots */}
        <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 pointer-events-auto">
          <div className="flex space-x-3">
            {phases.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentPhase(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentPhase
                    ? 'bg-amber-400 scale-125 shadow-lg shadow-amber-400/50'
                    : 'bg-white/30 hover:bg-white/50'
                }`}
                title={phases[index]}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ImmersiveHero;