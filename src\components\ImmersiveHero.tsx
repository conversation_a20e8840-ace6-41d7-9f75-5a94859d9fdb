import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Play, Award, Globe, TrendingUp, Users, Zap, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion, useAnimation, useScroll, useTransform } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import CountUp from 'react-countup';

const ImmersiveHero = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });
  
  const controls = useAnimation();
  const [isVisible, setIsVisible] = useState(false);
  const heroRef = useRef<HTMLElement>(null);
  
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const opacity = useTransform(scrollYProgress, [0, 0.5, 1], [1, 0.8, 0.3]);

  useEffect(() => {
    if (inView) {
      setIsVisible(true);
      controls.start('visible');
    }
  }, [controls, inView]);

  // Enhanced animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.15
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94] as const
      }
    }
  };

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      rotate: [0, 5, 0, -5, 0],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const pulseVariants = {
    animate: {
      scale: [1, 1.05, 1],
      opacity: [0.7, 1, 0.7],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <motion.section 
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900"
      style={{ y, opacity }}
    >
      {/* Immersive Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Animated Grid */}
        <motion.div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(rgba(245, 158, 11, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(245, 158, 11, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }}
          animate={{
            backgroundPosition: ['0px 0px', '50px 50px'],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        {/* Floating Geometric Shapes */}
        <motion.div
          className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-amber-400/20 to-amber-600/20 rounded-full blur-xl"
          variants={floatingVariants}
          animate="animate"
        />
        <motion.div
          className="absolute top-40 right-32 w-24 h-24 bg-gradient-to-r from-blue-400/20 to-blue-600/20 rounded-lg blur-xl"
          variants={floatingVariants}
          animate="animate"
          transition={{ delay: 1 }}
        />
        <motion.div
          className="absolute bottom-32 left-1/4 w-20 h-20 bg-gradient-to-r from-green-400/20 to-green-600/20 rounded-full blur-xl"
          variants={floatingVariants}
          animate="animate"
          transition={{ delay: 2 }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-28 h-28 bg-gradient-to-r from-purple-400/20 to-purple-600/20 rounded-lg blur-xl"
          variants={floatingVariants}
          animate="animate"
          transition={{ delay: 3 }}
        />

        {/* Particle Effect */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-amber-400 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -100, 0],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 relative z-10">
        <motion.div 
          ref={ref}
          className="max-w-6xl mx-auto text-center"
          variants={containerVariants}
          initial="hidden"
          animate={controls}
        >
          {/* Trust Badge */}
          <motion.div 
            className="inline-flex items-center space-x-3 bg-amber-500/20 border border-amber-500/30 rounded-full px-8 py-3 mb-8 backdrop-blur-sm"
            variants={itemVariants}
            whileHover={{ 
              scale: 1.05,
              backgroundColor: "rgba(245, 158, 11, 0.3)",
              transition: { duration: 0.2 }
            }}
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            >
              <Award className="w-5 h-5 text-amber-400" />
            </motion.div>
            <span className="text-amber-400 font-semibold text-lg">7+ Years International SEO Excellence</span>
            <motion.div variants={pulseVariants} animate="animate">
              <Star className="w-5 h-5 text-amber-400" />
            </motion.div>
          </motion.div>

          {/* Main Headline */}
          <motion.h1 
            className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight"
            variants={itemVariants}
          >
            <motion.span
              className="block mb-4"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
            >
              Transform Your Business
            </motion.span>
            <motion.span 
              className="bg-gradient-to-r from-amber-400 via-amber-500 to-amber-600 bg-clip-text text-transparent block"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.8 }}
              whileHover={{ 
                scale: 1.05,
                transition: { duration: 0.3 }
              }}
            >
              Into Global Empire
            </motion.span>
          </motion.h1>

          {/* Subheadline */}
          <motion.p 
            className="text-xl md:text-2xl lg:text-3xl text-slate-300 mb-12 max-w-4xl mx-auto leading-relaxed"
            variants={itemVariants}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 1.0 }}
          >
            International SEO consultant who built websites from zero to{' '}
            <motion.span 
              className="text-amber-400 font-semibold"
              whileHover={{ scale: 1.1 }}
            >
              10+ million monthly views
            </motion.span>
            {' '}across UK, US, Dubai, India, Kuwait & South Africa.
          </motion.p>

          {/* Enhanced Stats */}
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-3 gap-8 mb-16"
            variants={containerVariants}
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
          >
            {[
              { value: 10, suffix: 'M+', label: 'Monthly Views Generated', icon: TrendingUp },
              { value: 6, suffix: '', label: 'Countries Dominated', icon: Globe },
              { value: 7, suffix: '+', label: 'Years International Experience', icon: Users }
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="text-center p-6 bg-slate-800/50 rounded-2xl backdrop-blur-sm border border-slate-700/50"
                variants={itemVariants}
                whileHover={{
                  scale: 1.05,
                  backgroundColor: "rgba(30, 41, 59, 0.7)",
                  borderColor: "rgba(245, 158, 11, 0.3)",
                  transition: { duration: 0.3 }
                }}
              >
                <motion.div
                  className="w-16 h-16 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-4"
                  whileHover={{ rotate: 360, scale: 1.1 }}
                  transition={{ duration: 0.6 }}
                >
                  <stat.icon className="w-8 h-8 text-white" />
                </motion.div>
                <div className="text-4xl md:text-5xl font-bold text-amber-400 mb-2">
                  {isVisible && <CountUp end={stat.value} duration={2.5} suffix={stat.suffix} />}
                </div>
                <div className="text-slate-300 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>

          {/* Enhanced CTA Buttons */}
          <motion.div
            className="flex flex-col sm:flex-row gap-6 justify-center mb-16"
            variants={containerVariants}
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
          >
            <motion.div
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-xl px-12 py-6 rounded-full shadow-2xl hover:shadow-amber-500/25 transition-all duration-300">
                <Link to="/quote" className="flex items-center gap-3">
                  <Zap className="w-6 h-6" />
                  Get Premium SEO Strategy
                  <motion.div
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                  >
                    <ArrowRight className="w-6 h-6" />
                  </motion.div>
                </Link>
              </Button>
            </motion.div>

            <motion.div
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button asChild size="lg" variant="outline" className="border-2 border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-xl px-12 py-6 rounded-full backdrop-blur-sm transition-all duration-300">
                <Link to="/case-studies" className="flex items-center gap-3">
                  <Play className="w-6 h-6" />
                  View International Results
                </Link>
              </Button>
            </motion.div>
          </motion.div>

          {/* Client Success Stories */}
          <motion.div
            className="text-center"
            variants={itemVariants}
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
          >
            <motion.p
              className="text-slate-400 mb-8 text-lg"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 1.5 }}
            >
              Trusted by global businesses across 6 countries
            </motion.p>
            <motion.div
              className="flex flex-wrap justify-center items-center gap-6 opacity-70"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 0.7, y: 0 }}
              transition={{ duration: 1, delay: 1.8 }}
            >
              {['Easy Outdoor', 'Bulkland', 'Shakespeare Flowers', 'Opus Campers', 'Real Estate Leaders'].map((client, index) => (
                <motion.div
                  key={client}
                  className="bg-slate-800/50 rounded-xl px-6 py-3 text-white font-semibold backdrop-blur-sm border border-slate-700/50"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 2 + index * 0.1 }}
                  whileHover={{
                    scale: 1.05,
                    backgroundColor: "rgba(30, 41, 59, 0.8)",
                    borderColor: "rgba(245, 158, 11, 0.3)",
                    transition: { duration: 0.2 }
                  }}
                >
                  {client}
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 1, delay: 2.5 }}
      >
        <motion.div
          className="w-6 h-10 border-2 border-amber-400 rounded-full flex justify-center"
          animate={{ opacity: [1, 0.3, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <motion.div
            className="w-1 h-3 bg-amber-400 rounded-full mt-2"
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </motion.div>
      </motion.div>
    </motion.section>
  );
};

export default ImmersiveHero;
