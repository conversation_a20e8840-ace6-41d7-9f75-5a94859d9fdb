import React from 'react';
import { Link } from 'react-router-dom';
import { Building2, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const EnterpriseSeoServices = () => {
  const stats = [
    {
      metric: '450+',
      description: 'Enterprise Websites Optimized',
      detail: 'Fortune 500 & large corporations'
    },
    {
      metric: '5,200%',
      description: 'Average Enterprise Growth',
      detail: 'In organic visibility'
    },
    {
      metric: '₹2,850Cr+',
      description: 'Enterprise Revenue Generated',
      detail: 'Through SEO campaigns'
    },
    {
      metric: '99%',
      description: 'Enterprise Client Retention',
      detail: 'Long-term partnerships'
    }
  ];

  const achievements = [
    'Top Enterprise SEO Company in India',
    'Fortune 500 SEO Specialists',
    'Large-Scale SEO Experts',
    'Multi-National SEO Leaders',
    'Corporate SEO Champions',
    'Global Enterprise SEO Pioneers'
  ];

  const enterpriseSpecializations = [
    {
      type: 'Large-Scale Website SEO',
      description: 'Comprehensive SEO for enterprise websites with thousands of pages',
      icon: Building,
      features: ['Multi-Site SEO Management', 'Large-Scale Content Optimization', 'Enterprise Technical SEO', 'Global Website Optimization', 'Multi-Language SEO', 'Corporate Brand SEO']
    },
    {
      type: 'Multi-National SEO',
      description: 'Advanced SEO strategies for global enterprises and multi-national corporations',
      icon: Star,
      features: ['International SEO Strategy', 'Multi-Country Optimization', 'Global Market Penetration', 'Cross-Border SEO', 'Regional SEO Management', 'Global Brand Authority']
    },
    {
      type: 'Enterprise Technical SEO',
      description: 'Complex technical optimization for enterprise-level websites and platforms',
      icon: Crown,
      features: ['Enterprise Site Architecture', 'Complex Technical Audits', 'Large-Scale Performance Optimization', 'Enterprise Security SEO', 'API SEO Optimization', 'Enterprise Migration SEO']
    },
    {
      type: 'Corporate Governance & Compliance',
      description: 'SEO strategies aligned with corporate governance and compliance requirements',
      icon: Target,
      features: ['Compliance-Driven SEO', 'Corporate Policy Alignment', 'Risk Management SEO', 'Regulatory Compliance', 'Enterprise Reporting', 'Stakeholder Communication']
    }
  ];

  const enterpriseClients = [
    { name: 'Fortune 500', clients: '120+', growth: '5,400%' },
    { name: 'Multi-Nationals', clients: '180+', growth: '4,900%' },
    { name: 'Large Corporations', clients: '220+', growth: '4,600%' },
    { name: 'Global Brands', clients: '150+', growth: '5,200%' },
    { name: 'Enterprise SaaS', clients: '80+', growth: '5,800%' },
    { name: 'Conglomerates', clients: '60+', growth: '4,800%' }
  ];

  const caseStudies = [
    {
      client: 'Fortune 500 Technology Company',
      industry: 'Enterprise Technology',
      challenge: 'Global tech company needed SEO for 50+ country websites with 100,000+ pages',
      result: '5,800% global organic growth',
      metrics: ['15,000+ enterprise keywords in top 3', '₹1,200Cr+ enterprise revenue', '680% increase in global leads']
    },
    {
      client: 'Multi-National Manufacturing Corporation',
      industry: 'Global Manufacturing',
      challenge: 'Manufacturing giant needed SEO across 30+ countries with complex B2B requirements',
      result: '5,200% B2B lead generation',
      metrics: ['8,500+ B2B keywords ranking', '₹850Cr+ manufacturing revenue', '520% increase in qualified leads']
    },
    {
      client: 'Global Financial Services Enterprise',
      industry: 'Enterprise Financial Services',
      challenge: 'Financial enterprise needed compliant SEO across regulated markets worldwide',
      result: '4,900% compliant growth',
      metrics: ['6,200+ financial keywords in top 5', '₹650Cr+ financial services revenue', '420% increase in enterprise clients']
    }
  ];

  const enterpriseSeoStrategies = [
    {
      strategy: 'Enterprise-Scale SEO',
      description: 'Comprehensive SEO strategies designed for large-scale enterprise operations',
      benefits: ['Scalable optimization', 'Enterprise-grade results', 'Global reach', 'Corporate alignment']
    },
    {
      strategy: 'Multi-Site Management',
      description: 'Advanced management of multiple websites and international domains',
      benefits: ['Centralized control', 'Consistent branding', 'Global optimization', 'Resource efficiency']
    },
    {
      strategy: 'Corporate Compliance SEO',
      description: 'SEO strategies that align with corporate governance and regulatory requirements',
      benefits: ['Regulatory compliance', 'Risk mitigation', 'Corporate standards', 'Stakeholder confidence']
    },
    {
      strategy: 'Enterprise Analytics & Reporting',
      description: 'Advanced analytics and reporting for enterprise-level decision making',
      benefits: ['Executive dashboards', 'ROI measurement', 'Performance insights', 'Strategic planning']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-purple-500/20 border border-purple-500/30 rounded-full px-6 py-2 mb-8">
                    <Building2 className="w-4 h-4 text-purple-400" />
                    <span className="text-purple-400 font-medium">Enterprise SEO • Corporate Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Enterprise SEO Company in
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier enterprise SEO services offering comprehensive search engine optimization solutions for Fortune 500 companies, multi-national corporations, and large-scale enterprises. Our enterprise SEO company provides professional optimization services with large-scale website SEO, multi-national SEO strategies, enterprise technical SEO, and corporate governance compliance. Serving 450+ enterprise websites across global markets with proven ₹2,850Cr+ revenue generation and 5,200% average enterprise growth through strategic search engine optimization and corporate digital marketing excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Enterprise SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Enterprise SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-purple-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Enterprise SEO
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable enterprise SEO results from Fortune 500 companies and global corporations.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-purple-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Enterprise SEO Strategies We
                    <span className="bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized enterprise SEO strategies designed for large-scale corporate success and global market dominance.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {enterpriseSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-purple-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Enterprise Clients */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Enterprise Client Portfolio
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {enterpriseClients.map((client, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-purple-400 font-semibold mb-2">{client.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{client.clients}</div>
                      <div className="text-slate-400 text-sm mb-2">Enterprises Served</div>
                      <div className="text-green-400 font-semibold text-sm">{client.growth} avg growth</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Enterprise SEO Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced Enterprise SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {enterpriseSeoStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-purple-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-purple-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Enterprise SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-purple-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-purple-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Enterprise Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/seo/international" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">International SEO</h4>
                    <p className="text-slate-400 text-sm">Global SEO strategies for multi-national enterprises</p>
                  </Link>
                  <Link to="/services/seo/technical" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Technical SEO</h4>
                    <p className="text-slate-400 text-sm">Advanced technical optimization for enterprise websites</p>
                  </Link>
                  <Link to="/services/ai-automation" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">AI Automation</h4>
                    <p className="text-slate-400 text-sm">Enterprise-grade AI automation and optimization</p>
                  </Link>
                  <Link to="/services/business-automation" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Business Automation</h4>
                    <p className="text-slate-400 text-sm">Enterprise business process automation</p>
                  </Link>
                  <Link to="/services/business-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Business Development</h4>
                    <p className="text-slate-400 text-sm">Enterprise business development and growth strategies</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-purple-400 font-semibold mb-2 group-hover:text-purple-300">Enterprise Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our Fortune 500 clients</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-purple-600 to-purple-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Scale Your Enterprise SEO?</h2>
                <p className="text-xl mb-8">
                  Join 450+ Fortune 500 companies that trust GOD Digital Marketing for enterprise SEO excellence. Proven strategies that deliver 5,200% growth and ₹2,850Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-purple-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Enterprise SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Enterprise SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default EnterpriseSeoServices;
