import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SeoServicesKolkata = () => {
  const seoServices = [
    'Local SEO Services Kolkata',
    'International SEO Kolkata',
    'E-commerce SEO Kolkata',
    'Technical SEO Audit Kolkata',
    'Enterprise SEO Solutions Kolkata',
    'Mobile SEO Optimization Kolkata',
    'Voice Search SEO Kolkata',
    'Video SEO Services Kolkata',
    'Image SEO Optimization Kolkata',
    'Schema Markup Implementation Kolkata',
    'Core Web Vitals Optimization Kolkata',
    'Page Speed Optimization Kolkata',
    'SEO Content Strategy Kolkata',
    'Keyword Research & Analysis Kolkata',
    'Competitor SEO Analysis Kolkata',
    'SEO Link Building Kolkata',
    'Local Citation Building Kolkata',
    'Google My Business Optimization Kolkata',
    'Multi-location SEO Kolkata',
    'Cultural Heritage SEO Kolkata'
  ];

  const kolkataAreas = [
    'Salt Lake SEO Services',
    'Park Street SEO Company',
    'Ballygunge SEO Agency',
    'Alipore SEO Services',
    'New Town SEO Company',
    'Rajarhat SEO Services',
    'Howrah SEO Agency',
    'Jadavpur SEO Services',
    'Tollygunge SEO Company',
    'Behala SEO Services',
    'Garia SEO Agency',
    'Barasat SEO Services',
    'Dum Dum SEO Company',
    'Sealdah SEO Services',
    'Esplanade SEO Agency',
    'Gariahat SEO Services'
  ];

  const industries = [
    'Cultural Heritage SEO Kolkata',
    'Education SEO Kolkata',
    'Healthcare SEO Kolkata',
    'IT Services SEO Kolkata',
    'Manufacturing SEO Kolkata',
    'Jute Industry SEO Kolkata',
    'Tea Industry SEO Kolkata',
    'Financial Services SEO Kolkata',
    'Real Estate SEO Kolkata',
    'Hospitality SEO Kolkata',
    'E-commerce SEO Kolkata',
    'Government SEO Kolkata',
    'NGO & Social Services SEO Kolkata',
    'Art & Culture SEO Kolkata',
    'Food & Restaurant SEO Kolkata',
    'Publishing & Media SEO Kolkata'
  ];

  const seoPackages = [
    {
      name: 'Local SEO Kolkata Starter',
      price: '₹24,000',
      period: '/month',
      description: 'Perfect for small Kolkata businesses targeting local customers',
      features: [
        'Google My Business Optimization',
        'Local Keyword Research (50 keywords)',
        'On-Page SEO (10 pages)',
        'Local Citation Building (25 citations)',
        'Monthly Local SEO Report',
        'Kolkata Area Targeting'
      ]
    },
    {
      name: 'Kolkata SEO Professional',
      price: '₹44,000',
      period: '/month',
      description: 'Comprehensive SEO for growing Kolkata businesses',
      features: [
        'Everything in Starter',
        'Advanced Keyword Research (150 keywords)',
        'Technical SEO Audit & Fixes',
        'Content Marketing Strategy',
        'Link Building Campaign',
        'Competitor Analysis',
        'Multi-location SEO Setup'
      ],
      popular: true
    },
    {
      name: 'Enterprise SEO Kolkata',
      price: '₹78,000',
      period: '/month',
      description: 'Advanced SEO for large Kolkata enterprises',
      features: [
        'Everything in Professional',
        'Enterprise-level SEO Strategy',
        'Advanced Technical SEO',
        'International SEO Setup',
        'Custom SEO Dashboard',
        'Dedicated SEO Manager',
        'Priority Support'
      ]
    }
  ];

  const seoProcess = [
    {
      step: '1',
      title: 'Kolkata Market SEO Audit',
      description: 'Comprehensive analysis of your website\'s SEO performance in Kolkata\'s cultural market',
      icon: Search
    },
    {
      step: '2',
      title: 'Cultural-Focused Keyword Strategy',
      description: 'Strategic keyword research targeting Kolkata\'s cultural and educational sectors',
      icon: Target
    },
    {
      step: '3',
      title: 'Technical SEO Implementation',
      description: 'Advanced technical SEO optimization for better search engine performance',
      icon: Zap
    },
    {
      step: '4',
      title: 'Content & Link Building',
      description: 'High-quality content creation and authoritative link building for Kolkata market dominance',
      icon: TrendingUp
    }
  ];

  const stats = [
    {
      metric: '1400+',
      description: 'Kolkata Websites Optimized',
      detail: 'Across all industries'
    },
    {
      metric: '620%',
      description: 'Average Organic Traffic Increase',
      detail: 'For Kolkata businesses'
    },
    {
      metric: '₹38Cr+',
      description: 'Revenue Generated Through SEO',
      detail: 'For Kolkata clients'
    },
    {
      metric: '89%',
      description: 'First Page Rankings Achieved',
      detail: 'For target keywords'
    }
  ];

  const achievements = [
    'Google Premier Partner for Kolkata SEO',
    'Top SEO Company in Kolkata',
    'Cultural Heritage SEO Specialists',
    'Education Sector SEO Experts',
    'Healthcare SEO Leaders',
    'International SEO Consultants'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-blue-500/20 border border-blue-500/30 rounded-full px-6 py-2 mb-8">
                    <Search className="w-4 h-4 text-blue-400" />
                    <span className="text-blue-400 font-medium">SEO Services Kolkata • Cultural Capital SEO Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 SEO Services Company in
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Kolkata</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier SEO services in Kolkata offering comprehensive search engine optimization solutions from local SEO to enterprise-level strategies. Serving 1400+ Kolkata businesses across all areas - from cultural institutions in Park Street to IT companies in Salt Lake. Expert SEO solutions with proven ₹38Cr+ revenue generation and 620% average traffic increase for Kolkata clients in India's cultural capital.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Kolkata SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Kolkata SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-blue-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Kolkata SEO Services
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable SEO results from Kolkata businesses across all industries.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-blue-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* SEO Process Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Our Kolkata SEO
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Process</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Proven 4-step SEO methodology specifically designed for Kolkata's cultural and educational market dynamics.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
                  {seoProcess.map((process, index) => (
                    <Card key={index} className="bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300">
                      <CardContent className="p-8 text-center">
                        <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                          <process.icon className="w-8 h-8 text-white" />
                        </div>
                        <div className="text-3xl font-bold text-blue-400 mb-4">Step {process.step}</div>
                        <h3 className="text-xl font-bold text-white mb-4">{process.title}</h3>
                        <p className="text-slate-300">{process.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Kolkata SEO Services
                    <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive SEO pricing packages designed for Kolkata's cultural and educational business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {seoPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-blue-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-blue-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-blue-600 to-blue-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Kolkata Search Results?</h2>
                <p className="text-xl mb-8">
                  Join 1400+ Kolkata businesses that trust GOD Digital Marketing for SEO success. Proven strategies that deliver 620% traffic increase and ₹38Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Kolkata SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4">
                    <Link to="/contact">Call SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="seo" currentLocation="kolkata" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SeoServicesKolkata;
