
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Search, MousePointer, Share2, Mail, FileText, Globe, TrendingUp, Users, Award, CheckCircle, Star, Bot, Target, Building } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const Services = () => {
  const serviceCategories = [
    {
      category: 'SEO Services',
      icon: Search,
      description: 'International SEO expertise across 6 countries with proven results from zero to 10+ million monthly views.',
      color: 'from-blue-500 to-cyan-500',
      services: [
        {
          title: 'International SEO',
          description: 'Multi-country SEO campaigns that dominate global markets. Proven expertise across UK, US, Dubai, India, Kuwait & South Africa.',
          features: ['Multi-Country Ranking', 'International Keyword Research', 'Cross-Continental Link Building', 'Cultural Market Analysis'],
          results: '10M+ monthly views generated',
          link: '/services/seo/international'
        },
        {
          title: 'Local SEO',
          description: 'Dominate local search results and drive foot traffic with comprehensive local SEO strategies.',
          features: ['Google My Business Optimization', 'Local Citation Building', 'Review Management', 'Local Content Strategy'],
          results: '800% local visibility increase',
          link: '/services/seo/local'
        },
        {
          title: 'E-commerce SEO',
          description: 'Scale your online store with SEO strategies designed specifically for e-commerce success.',
          features: ['Product Page Optimization', 'Category Structure', 'Technical E-commerce SEO', 'Shopping Feed Optimization'],
          results: '450% organic traffic growth',
          link: '/services/seo/ecommerce'
        }
      ]
    },
    {
      category: 'AI Automation',
      icon: Bot,
      description: 'Advanced AI-powered automation that scales content creation and marketing operations while you focus on business growth.',
      color: 'from-purple-500 to-violet-500',
      services: [
        {
          title: 'AI Content Creation',
          description: 'Scale content production with advanced AI automation while maintaining quality and relevance for each market.',
          features: ['50-100 Content Pieces Monthly', 'Multi-Language Content', 'SEO-Optimized Creation', 'Quality Control Systems'],
          results: '500% content output increase',
          link: '/services/ai-content'
        },
        {
          title: 'Social Media Automation',
          description: 'Complete multi-platform automation that creates Google authority signals and brand recognition.',
          features: ['Multi-Platform Posting', 'Engagement Automation', 'Content Repurposing', 'Analytics Automation'],
          results: '24/7 marketing automation',
          link: '/services/social-media'
        },
        {
          title: 'Marketing Automation',
          description: 'End-to-end marketing automation that nurtures leads and scales your business operations.',
          features: ['Lead Nurturing Sequences', 'CRM Integration', 'Email Automation', 'Sales Funnel Automation'],
          results: '300% lead conversion improvement',
          link: '/services/marketing-automation'
        }
      ]
    },
    {
      category: 'Paid Advertising',
      icon: Target,
      description: 'Premium advertising management across Google, Facebook, LinkedIn and YouTube with focus on international markets.',
      color: 'from-green-500 to-emerald-500',
      services: [
        {
          title: 'Google Ads Management',
          description: 'Expert Google Ads management with international targeting and advanced optimization strategies.',
          features: ['International Campaign Setup', 'Advanced Targeting', 'Conversion Optimization', 'Multi-Country Management'],
          results: '340% ROI improvement',
          link: '/services/ppc'
        },
        {
          title: 'Facebook & Instagram Ads',
          description: 'Social media advertising that builds brand awareness and drives conversions across global markets.',
          features: ['Creative Development', 'Audience Targeting', 'A/B Testing', 'Cross-Platform Campaigns'],
          results: '280% engagement increase',
          link: '/services/facebook-ads'
        },
        {
          title: 'LinkedIn Advertising',
          description: 'B2B advertising on LinkedIn for professional services and enterprise clients seeking international expansion.',
          features: ['B2B Targeting', 'Lead Generation', 'Professional Content', 'Industry-Specific Campaigns'],
          results: '225% B2B lead increase',
          link: '/services/linkedin-ads'
        }
      ]
    }
  ];

  const industries = [
    'Real Estate',
    'Healthcare',
    'Education',
    'Manufacturing',
    'E-commerce',
    'Restaurants',
    'Hotels',
    'Legal Services',
    'Financial Services',
    'Technology',
    'Automotive',
    'Fashion',
    'Jewelry',
    'Construction',
    'Agriculture',
    'Pharmaceuticals',
    'Textiles',
    'Export-Import',
    'Logistics',
    'Retail',
    'Fitness',
    'Beauty & Wellness',
    'Tourism',
    'Event Management',
    'Consulting'
  ];

  const process = [
    {
      step: '01',
      title: 'Discovery & Analysis',
      description: 'We conduct comprehensive business analysis, competitor research, and market evaluation to understand your unique challenges and opportunities.'
    },
    {
      step: '02',
      title: 'Strategy Development',
      description: 'Our experts craft customized digital marketing strategies aligned with your business goals, target audience, and competitive landscape.'
    },
    {
      step: '03',
      title: 'Implementation & Optimization',
      description: 'We execute campaigns with precision, continuously monitoring performance and optimizing for maximum ROI and sustainable growth.'
    },
    {
      step: '04',
      title: 'Reporting & Scaling',
      description: 'Receive detailed performance reports and strategic recommendations to scale successful campaigns and maximize long-term results.'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <Globe className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">International SEO & AI Automation Specialist • 6 Countries</span>
              </div>

              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                International SEO & AI Automation
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Services</span>
              </h1>

              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Unlike local agencies that only know Indian SEO, we've built websites from zero to 10+ million monthly views across UK, US, Dubai, India, Kuwait & South Africa. Premium AI-powered strategies for businesses ready to dominate global markets.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get International Strategy</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Global Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Service Categories */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Premium International SEO &
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> AI Automation</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Specialized services for businesses ready to dominate international markets. From zero to millions of monthly views across 6 countries - proven strategies that deliver premium results.
              </p>
            </div>

            <div className="space-y-16">
              {serviceCategories.map((category, categoryIndex) => (
                <div key={categoryIndex}>
                  <div className="text-center mb-12">
                    <div className={`w-16 h-16 bg-gradient-to-r ${category.color} rounded-2xl flex items-center justify-center mx-auto mb-6`}>
                      <category.icon className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-3xl font-bold text-white mb-4">{category.category}</h3>
                    <p className="text-slate-300 max-w-2xl mx-auto">{category.description}</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {category.services.map((service, serviceIndex) => (
                      <Card key={serviceIndex} className="bg-slate-900/80 border-slate-700 hover:border-amber-500/40 transition-all duration-300 group">
                        <CardContent className="p-8">
                          <h4 className="text-xl font-bold text-white mb-4">{service.title}</h4>
                          <p className="text-slate-300 mb-6 leading-relaxed">{service.description}</p>

                          <ul className="space-y-2 mb-6">
                            {service.features.map((feature, idx) => (
                              <li key={idx} className="flex items-center text-slate-400 text-sm">
                                <CheckCircle className="w-3 h-3 text-amber-400 mr-2 flex-shrink-0" />
                                {feature}
                              </li>
                            ))}
                          </ul>

                          <div className="border-t border-slate-700 pt-6">
                            <div className="flex items-center justify-between mb-4">
                              <span className="text-amber-400 font-semibold text-sm">{service.results}</span>
                              <div className="flex text-amber-400">
                                {[...Array(5)].map((_, i) => (
                                  <Star key={i} className="w-3 h-3 fill-current" />
                                ))}
                              </div>
                            </div>

                            <Button asChild className="w-full bg-slate-800 hover:bg-amber-600 text-white">
                              <Link to={service.link} className="flex items-center justify-center">
                                Learn More <ArrowRight className="w-4 h-4 ml-2" />
                              </Link>
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Process Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Our Proven
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Success Process</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Every successful digital marketing campaign follows a structured approach. Our battle-tested methodology ensures consistent results and sustainable growth for businesses of all sizes.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {process.map((item, index) => (
                <div key={index} className="text-center group">
                  <div className="w-20 h-20 bg-gradient-to-r from-amber-500 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                    <span className="text-2xl font-bold text-white">{item.step}</span>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">{item.title}</h3>
                  <p className="text-slate-300 leading-relaxed">{item.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Industries Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Industry
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Expertise</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Our diverse portfolio spans multiple industries, giving us unique insights into sector-specific challenges and opportunities. We understand what works across different markets and customer behaviors.
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              {industries.map((industry, index) => (
                <div key={index} className="bg-slate-900/60 border border-slate-700 rounded-lg p-6 text-center hover:border-amber-500/40 transition-colors">
                  <h3 className="text-white font-semibold">{industry}</h3>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Dominate Global Markets?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join the elite businesses investing ₹40K-120K monthly for international SEO dominance.
              7 years of proven results across UK, US, Dubai, India, Kuwait & South Africa.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get International SEO Strategy</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-amber-600 text-lg px-8 py-4">
                <Link to="/contact">Book Premium Consultation</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Services;
