import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Settings, Search, TrendingUp, Users, CheckCircle, Building, Star, Crown, Target, Zap, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const TechnicalSeoServices = () => {
  const stats = [
    {
      metric: '1,800+',
      description: 'Websites Technically Optimized',
      detail: 'Across all industries'
    },
    {
      metric: '3,200%',
      description: 'Average Performance Improvement',
      detail: 'In Core Web Vitals'
    },
    {
      metric: '₹680Cr+',
      description: 'Revenue Generated',
      detail: 'Through technical optimization'
    },
    {
      metric: '98%',
      description: 'Technical Issues Resolved',
      detail: 'Within 30 days'
    }
  ];

  const achievements = [
    'Top Technical SEO Company in India',
    'Core Web Vitals Specialists',
    'Site Speed Optimization Experts',
    'Crawl Budget Optimization Leaders',
    'Schema Markup Champions',
    'Technical Audit Pioneers'
  ];

  const technicalSpecializations = [
    {
      type: 'Site Speed & Performance',
      description: 'Comprehensive optimization for website speed and Core Web Vitals performance',
      icon: Building,
      features: ['Core Web Vitals Optimization', 'Page Speed Improvement', 'Image Optimization', 'Code Minification', 'CDN Implementation', 'Caching Optimization']
    },
    {
      type: 'Crawlability & Indexation',
      description: 'Advanced optimization for search engine crawling and indexation efficiency',
      icon: Star,
      features: ['XML Sitemap Optimization', 'Robots.txt Optimization', 'Crawl Budget Management', 'Internal Linking Structure', 'URL Structure Optimization', 'Canonical Tag Implementation']
    },
    {
      type: 'Schema & Structured Data',
      description: 'Strategic implementation of structured data and schema markup',
      icon: Crown,
      features: ['Schema Markup Implementation', 'Rich Snippets Optimization', 'JSON-LD Implementation', 'Microdata Optimization', 'Open Graph Implementation', 'Twitter Cards Optimization']
    },
    {
      type: 'Mobile & Accessibility',
      description: 'Comprehensive mobile optimization and accessibility compliance',
      icon: Target,
      features: ['Mobile-First Optimization', 'Responsive Design SEO', 'AMP Implementation', 'Accessibility Compliance', 'Voice Search Optimization', 'Progressive Web App SEO']
    }
  ];

  const technicalAreas = [
    { name: 'Site Speed', improvement: '320%', metric: 'Faster Load Times' },
    { name: 'Core Web Vitals', improvement: '280%', metric: 'Performance Score' },
    { name: 'Crawl Efficiency', improvement: '420%', metric: 'Indexation Rate' },
    { name: 'Mobile Performance', improvement: '380%', metric: 'Mobile Score' },
    { name: 'Schema Implementation', improvement: '450%', metric: 'Rich Snippets' },
    { name: 'Technical Errors', improvement: '95%', metric: 'Error Reduction' }
  ];

  const caseStudies = [
    {
      client: 'Large E-commerce Platform',
      industry: 'E-commerce Technology',
      challenge: 'E-commerce site had severe technical SEO issues affecting 10,000+ product pages',
      result: '4,200% technical performance improvement',
      metrics: ['95% reduction in technical errors', '₹280Cr+ revenue increase', '380% improvement in Core Web Vitals']
    },
    {
      client: 'Enterprise SaaS Company',
      industry: 'Software Technology',
      challenge: 'SaaS platform needed technical optimization for complex web application',
      result: '3,800% crawl efficiency improvement',
      metrics: ['98% indexation rate achieved', '₹185Cr+ SaaS revenue growth', '420% improvement in site speed']
    },
    {
      client: 'Media & Publishing Website',
      industry: 'Digital Publishing',
      challenge: 'News website needed technical optimization for thousands of articles',
      result: '3,600% mobile performance boost',
      metrics: ['92% mobile performance score', '₹125Cr+ ad revenue increase', '350% improvement in page experience']
    }
  ];

  const technicalSeoStrategies = [
    {
      strategy: 'Performance Optimization',
      description: 'Comprehensive website performance and speed optimization',
      benefits: ['Faster load times', 'Better user experience', 'Higher rankings', 'Improved conversions']
    },
    {
      strategy: 'Crawl Optimization',
      description: 'Advanced crawl budget and indexation optimization',
      benefits: ['Efficient crawling', 'Better indexation', 'Faster discovery', 'Resource optimization']
    },
    {
      strategy: 'Structured Data Implementation',
      description: 'Strategic schema markup and structured data optimization',
      benefits: ['Rich snippets', 'Enhanced SERP presence', 'Better click-through rates', 'Voice search optimization']
    },
    {
      strategy: 'Mobile-First Optimization',
      description: 'Comprehensive mobile and accessibility optimization',
      benefits: ['Mobile performance', 'Accessibility compliance', 'Voice search ready', 'Progressive enhancement']
    }
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-cyan-500/20 border border-cyan-500/30 rounded-full px-6 py-2 mb-8">
                    <Settings className="w-4 h-4 text-cyan-400" />
                    <span className="text-cyan-400 font-medium">Technical SEO • Website Optimization Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Technical SEO Company in
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> India</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier technical SEO services offering comprehensive website optimization and technical performance solutions for enterprises, e-commerce platforms, and complex websites. Our technical SEO company provides professional optimization services with Core Web Vitals optimization, site speed improvement, crawl budget optimization, and schema markup implementation. Serving 1,800+ websites across all industries with proven ₹680Cr+ revenue generation and 3,200% average performance improvement through strategic technical SEO and website optimization excellence.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Technical SEO Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Technical SEO Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-cyan-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Technical SEO
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable technical SEO results from websites across all industries and platforms.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-cyan-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Technical SEO Strategies We
                    <span className="bg-gradient-to-r from-cyan-400 to-cyan-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized technical SEO strategies designed for optimal website performance and search engine compatibility.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {technicalSpecializations.map((spec, index) => (
                    <Card key={index} className="bg-slate-900/80 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center mb-6">
                          <spec.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{spec.type}</h3>
                        <p className="text-slate-300 mb-6">{spec.description}</p>
                        <ul className="space-y-2">
                          {spec.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-cyan-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Technical Areas */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Technical Optimization Areas
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                  {technicalAreas.map((area, index) => (
                    <div key={index} className="text-center bg-slate-900/50 rounded-lg p-4">
                      <h4 className="text-cyan-400 font-semibold mb-2">{area.name}</h4>
                      <div className="text-2xl font-bold text-white mb-1">{area.improvement}</div>
                      <div className="text-slate-400 text-sm">{area.metric}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Technical SEO Strategies */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Advanced Technical SEO Strategies
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {technicalSeoStrategies.map((strategy, index) => (
                    <div key={index} className="bg-slate-900/50 rounded-lg p-6">
                      <h4 className="text-cyan-400 font-semibold mb-3">{strategy.strategy}</h4>
                      <p className="text-slate-300 text-sm mb-4">{strategy.description}</p>
                      <ul className="space-y-1">
                        {strategy.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center">
                            <CheckCircle className="w-3 h-3 text-cyan-400 mr-2 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </section>

              {/* Case Studies */}
              <section className="mb-16">
                <h3 className="text-2xl font-bold text-white mb-8 text-center">
                  Technical SEO Success Stories
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {caseStudies.map((study, index) => (
                    <Card key={index} className="bg-slate-900/60 border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <h4 className="text-cyan-400 font-semibold mb-2">{study.client}</h4>
                        <p className="text-slate-400 text-sm mb-3">{study.industry}</p>
                        <p className="text-slate-300 text-sm mb-4">{study.challenge}</p>
                        <div className="bg-slate-800/50 rounded p-3 mb-4">
                          <div className="text-green-400 font-semibold mb-2">{study.result}</div>
                          <ul className="space-y-1">
                            {study.metrics.map((metric, idx) => (
                              <li key={idx} className="text-slate-400 text-xs flex items-center">
                                <CheckCircle className="w-3 h-3 text-cyan-400 mr-2 flex-shrink-0" />
                                {metric}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Internal Linking Section */}
              <section className="mb-16 bg-slate-800/30 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  Complete Technical Digital Marketing Solutions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link to="/services/seo" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">SEO Services</h4>
                    <p className="text-slate-400 text-sm">Comprehensive SEO services with technical optimization</p>
                  </Link>
                  <Link to="/services/web-development" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Web Development</h4>
                    <p className="text-slate-400 text-sm">Technical website development and optimization</p>
                  </Link>
                  <Link to="/services/seo/ecommerce" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">E-commerce SEO</h4>
                    <p className="text-slate-400 text-sm">Technical optimization for e-commerce platforms</p>
                  </Link>
                  <Link to="/services/seo/enterprise" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Enterprise SEO</h4>
                    <p className="text-slate-400 text-sm">Technical SEO for large enterprise websites</p>
                  </Link>
                  <Link to="/services/ai-automation" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">AI Automation</h4>
                    <p className="text-slate-400 text-sm">AI-powered technical optimization and automation</p>
                  </Link>
                  <Link to="/portfolio" className="bg-slate-900/50 rounded-lg p-4 hover:bg-slate-800/50 transition-colors group">
                    <h4 className="text-cyan-400 font-semibold mb-2 group-hover:text-cyan-300">Technical SEO Success Stories</h4>
                    <p className="text-slate-400 text-sm">See proven results from our technical optimization projects</p>
                  </Link>
                </div>
              </section>

              <section className="bg-gradient-to-r from-cyan-600 to-cyan-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Optimize Your Website's Technical Performance?</h2>
                <p className="text-xl mb-8">
                  Join 1,800+ websites that trust GOD Digital Marketing for technical SEO excellence. Proven strategies that deliver 3,200% performance improvement and ₹680Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-cyan-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Technical SEO Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-cyan-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Technical SEO Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            <div className="lg:col-span-1">
              <Sidebar currentService="seo" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default TechnicalSeoServices;
