import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Share2, TrendingUp, Users, CheckCircle, Building, Star, Crown, Heart, MessageCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SocialMediaHyderabad = () => {
  const socialMediaServices = [
    'Social Media Marketing Hyderabad',
    'Facebook Marketing Hyderabad',
    'Instagram Marketing Hyderabad',
    'LinkedIn Marketing Hyderabad',
    'Twitter Marketing Hyderabad',
    'YouTube Marketing Hyderabad',
    'Social Media Strategy Hyderabad',
    'Content Creation Hyderabad',
    'Community Management Hyderabad',
    'Influencer Marketing Hyderabad',
    'Social Media Advertising Hyderabad',
    'Brand Management Hyderabad',
    'Social Media Analytics Hyderabad',
    'Reputation Management Hyderabad',
    'Social Commerce Hyderabad',
    'Video Marketing Hyderabad',
    'IT Company Social Media Hyderabad',
    'Pharma Social Media Hyderabad',
    'Startup Social Media Hyderabad',
    'B2B Social Media Hyderabad'
  ];

  const platformStrategies = [
    {
      platform: 'LinkedIn for IT Companies',
      description: 'Professional networking and B2B lead generation for Hyderabad\'s IT sector',
      icon: Share2,
      features: ['Thought Leadership', 'Employee Advocacy', 'B2B Lead Generation', 'Industry Networking']
    },
    {
      platform: 'Instagram for Startups',
      description: 'Visual storytelling and brand building for Hyderabad\'s startup ecosystem',
      icon: Heart,
      features: ['Brand Storytelling', 'Product Showcases', 'Behind-the-Scenes', 'Startup Culture']
    },
    {
      platform: 'Facebook for Local Business',
      description: 'Community engagement and local marketing for Hyderabad businesses',
      icon: Users,
      features: ['Local Community Building', 'Event Promotion', 'Customer Service', 'Local Advertising']
    },
    {
      platform: 'YouTube for Education',
      description: 'Educational content and thought leadership for Hyderabad\'s knowledge economy',
      icon: MessageCircle,
      features: ['Educational Content', 'Webinar Promotion', 'Expert Interviews', 'Tutorial Videos']
    }
  ];

  const socialMediaPackages = [
    {
      name: 'Social Media Hyderabad Starter',
      price: '₹22,000',
      period: '/month',
      description: 'Perfect for small Hyderabad businesses starting with social media',
      features: [
        '3 Social Media Platforms',
        '20 Posts per Month',
        'Basic Graphics & Content',
        'Community Management',
        'Monthly Analytics Report',
        'Hyderabad Local Targeting'
      ]
    },
    {
      name: 'Hyderabad Social Media Professional',
      price: '₹42,000',
      period: '/month',
      description: 'Comprehensive social media management for growing Hyderabad businesses',
      features: [
        '5 Social Media Platforms',
        '40 Posts per Month',
        'Custom Graphics & Videos',
        'Influencer Collaborations',
        'Paid Social Advertising',
        'Advanced Analytics',
        'Dedicated Account Manager'
      ],
      popular: true
    },
    {
      name: 'Enterprise Social Media Hyderabad',
      price: '₹75,000',
      period: '/month',
      description: 'Advanced social media solutions for large Hyderabad enterprises',
      features: [
        'All Major Platforms',
        'Unlimited Content Creation',
        'Video Production',
        'Crisis Management',
        'Employee Advocacy Programs',
        'Advanced Automation',
        'Priority Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '850+',
      description: 'Social Media Accounts Managed',
      detail: 'For Hyderabad businesses'
    },
    {
      metric: '280%',
      description: 'Average Engagement Increase',
      detail: 'Across all platforms'
    },
    {
      metric: '₹18Cr+',
      description: 'Revenue Generated',
      detail: 'Through social media campaigns'
    },
    {
      metric: '92%',
      description: 'Client Satisfaction Rate',
      detail: 'Consistent quality delivery'
    }
  ];

  const achievements = [
    'Top Social Media Agency in Hyderabad',
    'IT Industry Social Media Specialists',
    'Startup Growth Marketing Experts',
    'B2B LinkedIn Marketing Leaders',
    'Pharmaceutical Social Media Champions',
    'Influencer Marketing Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-pink-500/20 border border-pink-500/30 rounded-full px-6 py-2 mb-8">
                    <Share2 className="w-4 h-4 text-pink-400" />
                    <span className="text-pink-400 font-medium">Social Media Marketing Hyderabad • Cyberabad Social Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Social Media Marketing Company in
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Hyderabad</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier social media marketing in Hyderabad offering comprehensive social media management and strategy services. Serving 850+ Hyderabad businesses across all areas - from IT companies in Hitech City to pharmaceutical companies in Genome Valley. Expert social media solutions with proven ₹18Cr+ revenue generation and 280% average engagement increase for Hyderabad clients in India's Cyberabad.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Hyderabad Social Media Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-pink-500 text-pink-400 hover:bg-pink-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Social Media Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-pink-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Hyderabad Social Media Marketing
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable social media results from Hyderabad businesses across all industries.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-pink-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Platform Strategies Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Platform Strategies We
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized social media strategies designed for Hyderabad's IT, pharmaceutical, and startup landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {platformStrategies.map((strategy, index) => (
                    <Card key={index} className="bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg flex items-center justify-center mb-6">
                          <strategy.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{strategy.platform}</h3>
                        <p className="text-slate-300 mb-6">{strategy.description}</p>
                        <ul className="space-y-2">
                          {strategy.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-pink-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Hyderabad Social Media Marketing
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive social media pricing designed for Hyderabad's IT and pharmaceutical business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {socialMediaPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-pink-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-pink-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-pink-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-pink-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-pink-600 to-pink-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Hyderabad Social Media?</h2>
                <p className="text-xl mb-8">
                  Join 850+ Hyderabad businesses that trust GOD Digital Marketing for social media success. Proven strategies that deliver 280% engagement increase and ₹18Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-pink-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Social Media Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-pink-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Social Media Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="social-media" currentLocation="hyderabad" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SocialMediaHyderabad;
