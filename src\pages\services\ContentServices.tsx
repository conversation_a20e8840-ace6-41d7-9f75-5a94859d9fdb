
import React from 'react';
import { Link } from 'react-router-dom';
import { FileText, Edit, Video, Image, CheckCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const ContentServices = () => {
  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <FileText className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Content Marketing</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Attract & Convert with
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Compelling Content</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Establish thought leadership and drive conversions with strategic content that educates, engages, and inspires your audience to take action.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Content Strategy</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View Content Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Create Content That Converts?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Let's develop a content strategy that positions you as the industry leader and drives measurable business growth.
            </p>
            <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
              <Link to="/quote">Get Content Marketing Strategy</Link>
            </Button>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default ContentServices;
