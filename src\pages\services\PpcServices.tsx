
import React from 'react';
import { Link } from 'react-router-dom';
import { MousePointer, Target, TrendingUp, DollarSign, CheckCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const PpcServices = () => {
  const platforms = [
    {
      name: 'Google Ads',
      description: 'Dominate search results with targeted Google Ads campaigns',
      features: ['Search Campaigns', 'Display Network', 'Shopping Ads', 'YouTube Ads']
    },
    {
      name: 'Facebook & Instagram',
      description: 'Reach your audience on the world\'s largest social platforms',
      features: ['News Feed Ads', 'Stories Ads', 'Video Campaigns', 'Retargeting']
    },
    {
      name: 'LinkedIn Advertising',
      description: 'Target professionals and decision-makers effectively',
      features: ['Sponsored Content', 'Message Ads', 'Lead Gen Forms', 'Account Targeting']
    },
    {
      name: 'Microsoft Ads',
      description: 'Capture additional market share with Bing advertising',
      features: ['Search Ads', 'Shopping Campaigns', 'Audience Network', 'Import from Google']
    }
  ];

  const results = [
    { metric: '340%', description: 'Average ROI improvement' },
    { metric: '65%', description: 'Reduction in cost per acquisition' },
    { metric: '180%', description: 'Increase in conversion rates' }
  ];

  return (
    <div className="min-h-screen bg-slate-900 text-white relative overflow-hidden">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
                <MousePointer className="w-4 h-4 text-amber-400" />
                <span className="text-amber-400 font-medium">Google Ads Management • PPC Advertising Services</span>
              </div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                Professional Google Ads &
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> PPC Management Services</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto">
                Our certified PPC specialists create and optimize high-performing campaigns across Google Ads, Facebook, LinkedIn, and more to deliver exceptional returns on your advertising investment.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free PPC Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/case-studies">View PPC Results</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Results Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {results.map((result, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-amber-400 mb-2">{result.metric}</div>
                  <div className="text-white font-semibold">{result.description}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Platforms Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Multi-Platform
                <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> PPC Expertise</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {platforms.map((platform, index) => (
                <Card key={index} className="bg-slate-900/80 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-4">{platform.name}</h3>
                    <p className="text-slate-300 mb-6">{platform.description}</p>
                    <ul className="space-y-2">
                      {platform.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-400">
                          <CheckCircle className="w-4 h-4 text-amber-400 mr-3" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Scale Your Advertising?</h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Get a free PPC audit and discover how we can optimize your campaigns for maximum ROI.
            </p>
            <Button asChild size="lg" className="bg-white text-amber-600 hover:bg-slate-100 text-lg px-8 py-4">
              <Link to="/quote">Get Free PPC Audit</Link>
            </Button>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default PpcServices;
