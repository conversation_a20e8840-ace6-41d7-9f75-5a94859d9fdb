
import React from 'react';

interface LogoStaticProps {
  width?: number;
  height?: number;
  className?: string;
}

const LogoStatic: React.FC<LogoStaticProps> = ({ 
  width = 500, 
  height = 500, 
  className = "" 
}) => {
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <svg
        width={width}
        height={height}
        viewBox="0 0 500 500"
        xmlns="http://www.w3.org/2000/svg"
        style={{ background: 'transparent' }}
      >
        {/* Background Circle with Gradient */}
        <defs>
          <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#1e293b" stopOpacity="0.8" />
            <stop offset="100%" stopColor="#0f172a" stopOpacity="0.9" />
          </radialGradient>
          <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#fbbf24" />
            <stop offset="50%" stopColor="#f59e0b" />
            <stop offset="100%" stopColor="#d97706" />
          </linearGradient>
          <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#ffffff" />
            <stop offset="100%" stopColor="#e5e7eb" />
          </linearGradient>
          <filter id="glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        {/* Main Background Circle */}
        <circle cx="250" cy="250" r="240" fill="url(#bgGradient)" stroke="#fbbf24" strokeWidth="2" opacity="0.1" />

        {/* Decorative Elements - Currency Symbols */}
        <g opacity="0.6">
          {/* Indian Rupee Symbols */}
          <text x="100" y="150" fontSize="24" fill="#fbbf24" opacity="0.7" fontFamily="Arial, sans-serif">₹</text>
          <text x="380" y="180" fontSize="20" fill="#fbbf24" opacity="0.5" fontFamily="Arial, sans-serif">₹</text>
          <text x="120" y="350" fontSize="18" fill="#fbbf24" opacity="0.6" fontFamily="Arial, sans-serif">₹</text>
          <text x="360" y="320" fontSize="22" fill="#fbbf24" opacity="0.4" fontFamily="Arial, sans-serif">₹</text>
        </g>

        {/* Growth Chart Elements */}
        <g opacity="0.3">
          <rect x="80" y="380" width="12" height="30" fill="#10b981" rx="2" />
          <rect x="95" y="370" width="12" height="40" fill="#10b981" rx="2" />
          <rect x="110" y="360" width="12" height="50" fill="#10b981" rx="2" />
          
          <rect x="350" y="100" width="10" height="25" fill="#10b981" rx="2" />
          <rect x="362" y="90" width="10" height="35" fill="#10b981" rx="2" />
          <rect x="374" y="80" width="10" height="45" fill="#10b981" rx="2" />
        </g>

        {/* Main Logo Letter 'G' */}
        <g filter="url(#glow)">
          <rect x="180" y="180" width="140" height="140" rx="20" fill="url(#logoGradient)" stroke="#f59e0b" strokeWidth="3" />
          <text x="250" y="270" fontSize="80" fontWeight="900" textAnchor="middle" fill="#0f172a" fontFamily="Arial Black, sans-serif">G</text>
          
          {/* Small indicator dot */}
          <circle cx="300" cy="190" r="8" fill="#10b981" stroke="#0f172a" strokeWidth="2" />
        </g>

        {/* Main Text 'GOD' */}
        <text x="250" y="360" fontSize="48" fontWeight="900" textAnchor="middle" fill="url(#textGradient)" fontFamily="Arial Black, sans-serif" filter="url(#glow)">GOD</text>
        
        {/* Subtitle 'Digital Marketing' */}
        <text x="250" y="390" fontSize="18" fontWeight="600" textAnchor="middle" fill="#fbbf24" fontFamily="Arial, sans-serif" letterSpacing="2px">DIGITAL MARKETING</text>

        {/* Decorative Lines */}
        <line x1="150" y1="400" x2="200" y2="400" stroke="#fbbf24" strokeWidth="2" opacity="0.6" />
        <line x1="300" y1="400" x2="350" y2="400" stroke="#fbbf24" strokeWidth="2" opacity="0.6" />
      </svg>
    </div>
  );
};

export default LogoStatic;
