import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Target, TrendingUp, Users, CheckCircle, Building, Star, Factory, Zap, DollarSign } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';

const GoogleAdsSonipat = () => {
  const googleAdsServices = [
    'Google Search Ads Sonipat',
    'Google Display Ads Sonipat',
    'Google Shopping Ads Sonipat',
    'YouTube Ads Management Sonipat',
    'Local Campaigns Sonipat',
    'Smart Campaigns Sonipat',
    'Performance Max Campaigns Sonipat',
    'Google Ads Remarketing Sonipat',
    'Dynamic Search Ads Sonipat',
    'Responsive Search Ads Sonipat',
    'Call-Only Campaigns Sonipat',
    'Industrial B2B Campaigns Sonipat',
    'Manufacturing Ads Sonipat',
    'Automobile Industry Ads Sonipat',
    'Textile Industry Ads Sonipat',
    'Google Ads Automation Sonipat',
    'Conversion Tracking Setup Sonipat',
    'Google Analytics Integration Sonipat',
    'Landing Page Optimization Sonipat',
    'Google Ads Account Audit Sonipat'
  ];

  const sonipatAreas = [
    'Sector 14 Google Ads',
    'Model Town PPC Services',
    'Rai Google Ads',
    'Gohana PPC Management',
    'Kharkhoda Google Ads',
    'Mundlana PPC Services',
    'Kathura Google Ads',
    'Gannaur PPC Management',
    'Industrial Area Google Ads',
    'Sonipat City PPC Services',
    'Delhi Road Google Ads',
    'GT Road PPC Management'
  ];

  const industries = [
    'Automobile Industry Google Ads Sonipat',
    'Textile Manufacturing Google Ads Sonipat',
    'Agriculture Google Ads Sonipat',
    'Food Processing Google Ads Sonipat',
    'Real Estate Google Ads Sonipat',
    'Healthcare Google Ads Sonipat',
    'Education Google Ads Sonipat',
    'Retail Google Ads Sonipat',
    'Construction Google Ads Sonipat',
    'Transportation Google Ads Sonipat',
    'Financial Services Google Ads Sonipat',
    'Government Sector Google Ads Sonipat'
  ];

  const googleAdsPackages = [
    {
      name: 'Google Ads Sonipat Starter',
      price: '₹12,000',
      period: '/month',
      adSpend: '+ Ad Spend',
      description: 'Perfect for small Sonipat businesses starting with Google Ads',
      features: [
        'Google Search Ads Setup',
        'Local Keyword Research (30 keywords)',
        'Ad Copy Creation (5 ads)',
        'Landing Page Optimization',
        'Conversion Tracking Setup',
        'Monthly Performance Report'
      ]
    },
    {
      name: 'Sonipat Google Ads Professional',
      price: '₹22,000',
      period: '/month',
      adSpend: '+ Ad Spend',
      description: 'Comprehensive Google Ads management for growing Sonipat businesses',
      features: [
        'Everything in Starter',
        'Display & Shopping Ads',
        'Industrial B2B Targeting',
        'Remarketing Campaigns',
        'Advanced Audience Targeting',
        'A/B Testing & Optimization',
        'Weekly Performance Reviews'
      ],
      popular: true
    },
    {
      name: 'Enterprise Google Ads Sonipat',
      price: '₹40,000',
      period: '/month',
      adSpend: '+ Ad Spend',
      description: 'Advanced Google Ads management for large Sonipat manufacturers',
      features: [
        'Everything in Professional',
        'Performance Max Campaigns',
        'Advanced B2B Automation',
        'Custom Industrial Audiences',
        'Cross-Platform Integration',
        'Dedicated Account Manager',
        'Priority Support'
      ]
    }
  ];

  const campaignTypes = [
    {
      type: 'Industrial Search Campaigns',
      description: 'Target B2B customers actively searching for industrial products and services in Sonipat',
      icon: Factory,
      benefits: ['B2B Lead Generation', 'Industrial Keywords', 'Local Targeting', 'Cost-Effective']
    },
    {
      type: 'Manufacturing Display Ads',
      description: 'Reach manufacturing decision-makers across relevant industry websites',
      icon: Building,
      benefits: ['Industry Targeting', 'Visual Impact', 'Brand Awareness', 'Remarketing']
    },
    {
      type: 'Local Shopping Campaigns',
      description: 'Showcase products directly in Google search results for Sonipat shoppers',
      icon: DollarSign,
      benefits: ['Product Visibility', 'Local Customers', 'Higher CTR', 'E-commerce Focus']
    },
    {
      type: 'YouTube B2B Campaigns',
      description: 'Engage industrial audiences with compelling video advertisements',
      icon: Zap,
      benefits: ['Video Engagement', 'B2B Targeting', 'Industry Focus', 'Brand Building']
    }
  ];

  const stats = [
    {
      metric: '120+',
      description: 'Sonipat Google Ads Campaigns',
      detail: 'Successfully managed'
    },
    {
      metric: '380%',
      description: 'Average ROAS Achieved',
      detail: 'For Sonipat businesses'
    },
    {
      metric: '₹6Cr+',
      description: 'Revenue Generated',
      detail: 'Through Google Ads'
    },
    {
      metric: '55%',
      description: 'Cost Reduction',
      detail: 'Average CPC optimization'
    }
  ];

  const achievements = [
    'Top Google Ads Agency in Sonipat',
    'Industrial B2B Specialists',
    'Manufacturing Ads Experts',
    'Automobile Industry Partners',
    'Textile Sector Leaders',
    'Local Business Champions'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-5xl mx-auto text-center">
              <div className="inline-flex items-center space-x-2 bg-orange-500/20 border border-orange-500/30 rounded-full px-6 py-2 mb-8">
                <Target className="w-4 h-4 text-orange-400" />
                <span className="text-orange-400 font-medium">Google Ads Sonipat • Industrial Gateway PPC Excellence</span>
              </div>
              
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                #1 Google Ads Company in
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Sonipat</span>
              </h1>
              
              <p className="text-xl text-slate-300 mb-12 max-w-4xl mx-auto">
                Premier Google Ads management in Sonipat offering comprehensive PPC solutions for industrial and manufacturing businesses. Serving 120+ Sonipat businesses across all sectors - from automobile manufacturers to textile industries in this industrial hub. Expert Google Ads solutions with proven ₹6Cr+ revenue generation and 380% average ROAS for Sonipat clients.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white text-lg px-8 py-4">
                  <Link to="/quote">Get Free Sonipat Google Ads Audit</Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-lg px-8 py-4">
                  <Link to="/contact">Call Sonipat PPC Experts: +91-9999-XXXX-XX</Link>
                </Button>
              </div>

              {/* Achievements */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                    <CheckCircle className="w-4 h-4 text-orange-400 flex-shrink-0" />
                    <span className="text-slate-300">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Sonipat Google Ads
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Success Metrics</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto mb-12">
                Measurable Google Ads results from Sonipat businesses across all industries - delivering exceptional ROAS for industrial and manufacturing sectors.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-orange-400 mb-2">{stat.metric}</div>
                  <div className="text-white font-semibold mb-1">{stat.description}</div>
                  <div className="text-slate-400">{stat.detail}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Campaign Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Google Ads Campaign Types We
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Manage</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Specialized Google Ads campaign management for Sonipat's industrial and manufacturing business landscape.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-20">
              {campaignTypes.map((campaign, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-6">
                      <campaign.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{campaign.type}</h3>
                    <p className="text-slate-300 mb-6">{campaign.description}</p>
                    <ul className="space-y-2">
                      {campaign.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-center text-slate-400 text-sm">
                          <CheckCircle className="w-3 h-3 text-orange-400 mr-2 flex-shrink-0" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Google Ads Services Grid */}
        <section className="py-20 bg-slate-800/50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Complete Google Ads Services
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Available in Sonipat</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Comprehensive range of Google Ads services covering every aspect of PPC advertising for Sonipat industrial businesses.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {googleAdsServices.map((service, index) => (
                <Card key={index} className="bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <Target className="w-8 h-8 text-orange-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-2 text-sm">{service}</h3>
                    <Button asChild size="sm" variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500 hover:text-white text-xs">
                      <Link to="/quote">Get Quote</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                Sonipat Google Ads Management
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Pricing</span>
              </h2>
              <p className="text-xl text-slate-300 max-w-4xl mx-auto">
                Affordable Google Ads management pricing designed for Sonipat businesses of all sizes.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {googleAdsPackages.map((pkg, index) => (
                <Card key={index} className={`bg-slate-900/80 border-orange-500/20 hover:border-orange-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-orange-500' : ''}`}>
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                  )}
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-slate-300 mb-6">{pkg.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-orange-400">{pkg.price}</span>
                      <span className="text-slate-400">{pkg.period}</span>
                      <div className="text-sm text-slate-400">{pkg.adSpend}</div>
                    </div>
                    <ul className="space-y-3 mb-8">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-slate-300">
                          <CheckCircle className="w-4 h-4 text-orange-400 mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                      <Link to="/quote">Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-orange-600 to-orange-500">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6">Ready to Dominate Sonipat with Google Ads?</h2>
            <p className="text-xl mb-8 max-w-4xl mx-auto">
              Join 120+ Sonipat businesses that trust GOD Digital Marketing for Google Ads success. Proven strategies that deliver 380% ROAS and ₹6Cr+ revenue generation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-orange-600 hover:bg-slate-100 text-lg px-8 py-4">
                <Link to="/quote">Get Free Google Ads Audit</Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 text-lg px-8 py-4">
                <Link to="/contact">Call PPC Experts: +91-9999-XXXX-XX</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default GoogleAdsSonipat;
