import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Share2, TrendingUp, Users, CheckCircle, Building, Star, Crown, Heart, MessageCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const SocialMediaKolkata = () => {
  const socialMediaServices = [
    'Social Media Marketing Kolkata',
    'Facebook Marketing Kolkata',
    'Instagram Marketing Kolkata',
    'LinkedIn Marketing Kolkata',
    'Twitter Marketing Kolkata',
    'YouTube Marketing Kolkata',
    'Social Media Strategy Kolkata',
    'Content Creation Kolkata',
    'Community Management Kolkata',
    'Influencer Marketing Kolkata',
    'Social Media Advertising Kolkata',
    'Brand Management Kolkata',
    'Social Media Analytics Kolkata',
    'Reputation Management Kolkata',
    'Social Commerce Kolkata',
    'Video Marketing Kolkata',
    'Education Social Media Kolkata',
    'Cultural Events Social Media Kolkata',
    'Healthcare Social Media Kolkata',
    'Bengali Content Creation Kolkata'
  ];

  const platformStrategies = [
    {
      platform: 'Facebook for Education',
      description: 'Educational institution marketing and student engagement for Kolkata\'s education sector',
      icon: Share2,
      features: ['Student Recruitment', 'Course Promotion', 'Educational Content', 'Parent Engagement']
    },
    {
      platform: 'Instagram for Culture',
      description: 'Cultural storytelling and heritage promotion for Kolkata\'s rich cultural landscape',
      icon: Heart,
      features: ['Cultural Events', 'Heritage Promotion', 'Festival Marketing', 'Art & Literature']
    },
    {
      platform: 'LinkedIn for Professionals',
      description: 'Professional networking and B2B engagement for Kolkata\'s business community',
      icon: Users,
      features: ['Professional Networking', 'B2B Lead Generation', 'Industry Insights', 'Career Development']
    },
    {
      platform: 'YouTube for Bengali Content',
      description: 'Bengali language content and regional marketing for Kolkata\'s local audience',
      icon: MessageCircle,
      features: ['Bengali Content', 'Regional Marketing', 'Cultural Videos', 'Local Storytelling']
    }
  ];

  const socialMediaPackages = [
    {
      name: 'Social Media Kolkata Starter',
      price: '₹18,000',
      period: '/month',
      description: 'Perfect for small Kolkata businesses starting with social media',
      features: [
        '3 Social Media Platforms',
        '20 Posts per Month',
        'Basic Graphics & Content',
        'Community Management',
        'Monthly Analytics Report',
        'Bengali Content Support'
      ]
    },
    {
      name: 'Kolkata Social Media Professional',
      price: '₹34,000',
      period: '/month',
      description: 'Comprehensive social media management for growing Kolkata businesses',
      features: [
        '5 Social Media Platforms',
        '40 Posts per Month',
        'Custom Graphics & Videos',
        'Influencer Collaborations',
        'Paid Social Advertising',
        'Advanced Analytics',
        'Dedicated Account Manager'
      ],
      popular: true
    },
    {
      name: 'Enterprise Social Media Kolkata',
      price: '₹62,000',
      period: '/month',
      description: 'Advanced social media solutions for large Kolkata enterprises',
      features: [
        'All Major Platforms',
        'Unlimited Content Creation',
        'Video Production',
        'Crisis Management',
        'Employee Advocacy Programs',
        'Advanced Automation',
        'Priority Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '720+',
      description: 'Social Media Accounts Managed',
      detail: 'For Kolkata businesses'
    },
    {
      metric: '240%',
      description: 'Average Engagement Increase',
      detail: 'Across all platforms'
    },
    {
      metric: '₹14Cr+',
      description: 'Revenue Generated',
      detail: 'Through social media campaigns'
    },
    {
      metric: '89%',
      description: 'Client Satisfaction Rate',
      detail: 'Consistent quality delivery'
    }
  ];

  const achievements = [
    'Top Social Media Agency in Kolkata',
    'Education Sector Social Media Specialists',
    'Cultural Marketing Experts',
    'Bengali Content Creation Leaders',
    'Healthcare Social Media Champions',
    'Tourism Marketing Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-pink-500/20 border border-pink-500/30 rounded-full px-6 py-2 mb-8">
                    <Share2 className="w-4 h-4 text-pink-400" />
                    <span className="text-pink-400 font-medium">Social Media Marketing Kolkata • Cultural Capital Social Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Social Media Marketing Company in
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Kolkata</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier social media marketing in Kolkata offering comprehensive social media management and strategy services. Serving 720+ Kolkata businesses across all areas - from educational institutions in College Street to cultural organizations in Rabindra Sarobar. Expert social media solutions with proven ₹14Cr+ revenue generation and 240% average engagement increase for Kolkata clients in India's Cultural Capital.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Kolkata Social Media Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-pink-500 text-pink-400 hover:bg-pink-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Social Media Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-pink-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Kolkata Social Media Marketing
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable social media results from Kolkata businesses across all industries.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-pink-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Platform Strategies Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Platform Strategies We
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Master</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized social media strategies designed for Kolkata's education, cultural, and healthcare landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {platformStrategies.map((strategy, index) => (
                    <Card key={index} className="bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg flex items-center justify-center mb-6">
                          <strategy.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{strategy.platform}</h3>
                        <p className="text-slate-300 mb-6">{strategy.description}</p>
                        <ul className="space-y-2">
                          {strategy.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-pink-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Kolkata Social Media Marketing
                    <span className="bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive social media pricing designed for Kolkata's education and cultural business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {socialMediaPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-pink-500/20 hover:border-pink-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-pink-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-pink-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-pink-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-pink-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-pink-600 to-pink-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Kolkata Social Media?</h2>
                <p className="text-xl mb-8">
                  Join 720+ Kolkata businesses that trust GOD Digital Marketing for social media success. Proven strategies that deliver 240% engagement increase and ₹14Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-pink-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Social Media Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-pink-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Social Media Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="social-media" currentLocation="kolkata" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default SocialMediaKolkata;
