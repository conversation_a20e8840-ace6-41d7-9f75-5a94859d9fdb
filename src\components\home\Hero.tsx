
import React, { lazy, Suspense } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Play, Award } from 'lucide-react';
import { Button } from '@/components/ui/button';

// Lazy load the 3D logo for better initial page load
const Logo3D = lazy(() => import('@/components/Logo3D'));

// Optimized fallback component
const LogoFallback = () => (
  <div className="w-full h-40 flex items-center justify-center mb-8">
    <div className="text-center animate-pulse">
      <div className="text-4xl font-bold text-white mb-2">GOD</div>
      <div className="text-lg text-amber-400">Digital Marketing</div>
    </div>
  </div>
);

const Hero = () => {
  return (
    <section className="pt-20 pb-32 relative">
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto text-center">
          {/* Lazy loaded 3D Logo with fallback */}
          <Suspense fallback={<LogoFallback />}>
            <Logo3D className="mb-8" />
          </Suspense>

          {/* Trust Badge */}
          <div className="inline-flex items-center space-x-2 bg-amber-500/20 border border-amber-500/30 rounded-full px-6 py-2 mb-8">
            <Award className="w-4 h-4 text-amber-400" />
            <span className="text-amber-400 font-medium">7 Years International SEO Experience • 6 Countries</span>
          </div>

          {/* Main Headline */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
            Scale Your Business
            <span className="bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent"> Globally</span>
          </h1>

          {/* Subheadline */}
          <p className="text-lg md:text-xl lg:text-2xl text-slate-300 mb-12 max-w-3xl mx-auto leading-relaxed">
            International SEO consultant who built websites from zero to 10+ million monthly views across UK, US, Dubai, India, Kuwait & South Africa.
            Premium AI-powered strategies for businesses ready to dominate global markets.
          </p>

          {/* Stats - Optimized for mobile */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 md:gap-8 mb-12">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-amber-400 mb-2">10M+</div>
              <div className="text-sm md:text-base text-slate-300">Monthly Views Generated</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-amber-400 mb-2">6</div>
              <div className="text-sm md:text-base text-slate-300">Countries Dominated</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-amber-400 mb-2">7</div>
              <div className="text-sm md:text-base text-slate-300">Years International Experience</div>
            </div>
          </div>

          {/* CTA Buttons - Mobile optimized */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <Button asChild size="lg" className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-base md:text-lg px-6 md:px-8 py-3 md:py-4">
              <Link to="/quote" className="flex items-center justify-center">
                Get Premium SEO Consultation
                <ArrowRight className="ml-2 w-4 h-4 md:w-5 md:h-5" />
              </Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white text-base md:text-lg px-6 md:px-8 py-3 md:py-4">
              <Link to="/case-studies" className="flex items-center justify-center">
                <Play className="mr-2 w-4 h-4 md:w-5 md:h-5" />
                View International Results
              </Link>
            </Button>
          </div>

          {/* Client Logos - Mobile optimized */}
          <div className="text-center">
            <p className="text-slate-400 mb-6 md:mb-8 text-sm md:text-base">Proven results across global markets</p>
            <div className="flex flex-wrap justify-center items-center gap-4 md:gap-8 opacity-60">
              <div className="bg-white/10 rounded-lg px-4 py-2 md:px-6 md:py-3 text-white font-semibold text-sm md:text-base">Easy Outdoor</div>
              <div className="bg-white/10 rounded-lg px-4 py-2 md:px-6 md:py-3 text-white font-semibold text-sm md:text-base">Bulkland</div>
              <div className="bg-white/10 rounded-lg px-4 py-2 md:px-6 md:py-3 text-white font-semibold text-sm md:text-base">Shakespeare Flowers</div>
              <div className="bg-white/10 rounded-lg px-4 py-2 md:px-6 md:py-3 text-white font-semibold text-sm md:text-base">Opus Campers</div>
              <div className="bg-white/10 rounded-lg px-4 py-2 md:px-6 md:py-3 text-white font-semibold text-sm md:text-base">Real Estate Leaders</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
