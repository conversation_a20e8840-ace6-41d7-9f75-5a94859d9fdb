import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, FileText, TrendingUp, Users, CheckCircle, Building, Star, Crown, Edit, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import MoneyBackground from '@/components/MoneyBackground';
import Sidebar from '@/components/Sidebar';

const ContentMarketingKolkata = () => {
  const contentServices = [
    'Content Marketing Strategy Kolkata',
    'Content Creation Services Kolkata',
    'Blog Writing Services Kolkata',
    'Copywriting Services Kolkata',
    'Technical Writing Kolkata',
    'Website Content Writing Kolkata',
    'SEO Content Writing Kolkata',
    'Social Media Content Kolkata',
    'Email Content Creation Kolkata',
    'Video Content Scripts Kolkata',
    'Infographic Content Kolkata',
    'White Paper Writing Kolkata',
    'Case Study Writing Kolkata',
    'Press Release Writing Kolkata',
    'Product Description Writing Kolkata',
    'Educational Content Kolkata',
    'Healthcare Content Kolkata',
    'Cultural Content Writing Kolkata',
    'Bengali Content Creation Kolkata',
    'Tourism Content Marketing Kolkata'
  ];

  const contentTypes = [
    {
      type: 'Educational Content',
      description: 'Academic and educational content for Kolkata\'s educational institutions',
      icon: Edit,
      features: ['Course Content', 'Educational Blogs', 'Academic Papers', 'Student Resources']
    },
    {
      type: 'Cultural Content',
      description: 'Heritage and cultural content for Kolkata\'s rich cultural landscape',
      icon: Star,
      features: ['Cultural Stories', 'Heritage Content', 'Festival Marketing', 'Art & Literature']
    },
    {
      type: 'Healthcare Content',
      description: 'Medical and healthcare content for Kolkata\'s healthcare sector',
      icon: Zap,
      features: ['Medical Articles', 'Health Awareness', 'Patient Education', 'Healthcare Blogs']
    },
    {
      type: 'Bengali Content Creation',
      description: 'Regional content in Bengali for Kolkata\'s local audience',
      icon: FileText,
      features: ['Bengali Copywriting', 'Regional Marketing', 'Local Stories', 'Cultural Content']
    }
  ];

  const contentPackages = [
    {
      name: 'Content Marketing Kolkata Starter',
      price: '₹22,000',
      period: '/month',
      description: 'Perfect for small Kolkata businesses starting with content marketing',
      features: [
        '8 Blog Posts per Month',
        'SEO-Optimized Content',
        'Social Media Content (20 posts)',
        'Basic Graphics & Images',
        'Content Calendar',
        'Bengali Content Support'
      ]
    },
    {
      name: 'Kolkata Content Marketing Professional',
      price: '₹38,000',
      period: '/month',
      description: 'Comprehensive content marketing for growing Kolkata businesses',
      features: [
        '16 Blog Posts per Month',
        'Website Content Updates',
        'Social Media Content (40 posts)',
        'Email Newsletter Content',
        'Video Script Writing',
        'Advanced SEO Content',
        'Cultural Content Strategy'
      ],
      popular: true
    },
    {
      name: 'Enterprise Content Marketing Kolkata',
      price: '₹68,000',
      period: '/month',
      description: 'Advanced content marketing for large Kolkata enterprises',
      features: [
        'Unlimited Content Creation',
        'Multi-language Content',
        'Video Content Production',
        'White Papers & Case Studies',
        'Thought Leadership Content',
        'Dedicated Content Manager',
        'Priority Support'
      ]
    }
  ];

  const stats = [
    {
      metric: '1800+',
      description: 'Content Pieces Created',
      detail: 'For Kolkata businesses'
    },
    {
      metric: '320%',
      description: 'Average Traffic Increase',
      detail: 'Through content marketing'
    },
    {
      metric: '₹22Cr+',
      description: 'Revenue Generated',
      detail: 'Through content-driven leads'
    },
    {
      metric: '82%',
      description: 'Lead Quality Improvement',
      detail: 'With targeted content'
    }
  ];

  const achievements = [
    'Top Content Marketing Agency in Kolkata',
    'Educational Content Specialists',
    'Cultural Content Experts',
    'Bengali Content Creation Leaders',
    'Healthcare Content Champions',
    'Tourism Content Marketing Pioneers'
  ];

  return (
    <div className="min-h-screen bg-slate-900 relative">
      <MoneyBackground />
      <Navigation />
      
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Hero Section */}
              <section className="pt-24 pb-16">
                <div className="max-w-4xl">
                  <div className="inline-flex items-center space-x-2 bg-indigo-500/20 border border-indigo-500/30 rounded-full px-6 py-2 mb-8">
                    <FileText className="w-4 h-4 text-indigo-400" />
                    <span className="text-indigo-400 font-medium">Content Marketing Kolkata • Cultural Capital Content Excellence</span>
                  </div>
                  
                  <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    #1 Content Marketing Company in
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Kolkata</span>
                  </h1>
                  
                  <p className="text-xl text-slate-300 mb-8">
                    Premier content marketing in Kolkata offering comprehensive content creation and strategy services from educational content to cultural storytelling. Serving 1800+ Kolkata businesses across all areas - from educational institutions in College Street to cultural organizations in Rabindra Sarobar. Expert content marketing solutions with proven ₹22Cr+ revenue generation and 320% average traffic increase for Kolkata clients in India's Cultural Capital.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 mb-8">
                    <Button asChild size="lg" className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white text-lg px-8 py-4">
                      <Link to="/quote">Get Free Kolkata Content Audit</Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="border-indigo-500 text-indigo-400 hover:bg-indigo-500 hover:text-white text-lg px-8 py-4">
                      <Link to="/contact">Call Content Experts: +91-8708577598</Link>
                    </Button>
                  </div>

                  {/* Achievements */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center justify-center space-x-2 bg-slate-800/50 rounded-lg p-3">
                        <CheckCircle className="w-4 h-4 text-indigo-400 flex-shrink-0" />
                        <span className="text-slate-300">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Stats Section */}
              <section className="mb-16 bg-slate-800/50 rounded-2xl p-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Kolkata Content Marketing
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Success Metrics</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Measurable content marketing results from Kolkata businesses across all industries.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-indigo-400 mb-2">{stat.metric}</div>
                      <div className="text-white font-semibold mb-1">{stat.description}</div>
                      <div className="text-slate-400">{stat.detail}</div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Content Types Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Content Types We
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Create</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Specialized content creation services for Kolkata's educational, cultural, and healthcare landscape.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {contentTypes.map((content, index) => (
                    <Card key={index} className="bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300">
                      <CardContent className="p-8">
                        <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mb-6">
                          <content.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4">{content.type}</h3>
                        <p className="text-slate-300 mb-6">{content.description}</p>
                        <ul className="space-y-2">
                          {content.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-400 text-sm">
                              <CheckCircle className="w-3 h-3 text-indigo-400 mr-2 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* Pricing Section */}
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-5xl font-bold mb-6">
                    Kolkata Content Marketing
                    <span className="bg-gradient-to-r from-indigo-400 to-indigo-600 bg-clip-text text-transparent"> Pricing</span>
                  </h2>
                  <p className="text-xl text-slate-300">
                    Competitive content marketing pricing designed for Kolkata's educational and cultural business environment.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {contentPackages.map((pkg, index) => (
                    <Card key={index} className={`bg-slate-900/80 border-indigo-500/20 hover:border-indigo-500/40 transition-all duration-300 relative ${pkg.popular ? 'ring-2 ring-indigo-500' : ''}`}>
                      {pkg.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-indigo-500 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                      )}
                      <CardContent className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                        <p className="text-slate-300 mb-6">{pkg.description}</p>
                        <div className="mb-6">
                          <span className="text-4xl font-bold text-indigo-400">{pkg.price}</span>
                          <span className="text-slate-400">{pkg.period}</span>
                        </div>
                        <ul className="space-y-3 mb-8">
                          {pkg.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-slate-300">
                              <CheckCircle className="w-4 h-4 text-indigo-400 mr-3 flex-shrink-0" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                        <Button asChild className={`w-full ${pkg.popular ? 'bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700' : 'bg-slate-700 hover:bg-slate-600'} text-white`}>
                          <Link to="/quote">Get Started</Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>

              {/* CTA Section */}
              <section className="bg-gradient-to-r from-indigo-600 to-indigo-500 rounded-2xl p-8 text-center">
                <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to Dominate Kolkata with Content?</h2>
                <p className="text-xl mb-8">
                  Join 1800+ Kolkata businesses that trust GOD Digital Marketing for content marketing success. Proven strategies that deliver 320% traffic increase and ₹22Cr+ revenue generation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-indigo-600 hover:bg-slate-100 text-lg px-8 py-4">
                    <Link to="/quote">Get Free Content Audit</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-indigo-600 text-lg px-8 py-4">
                    <Link to="/contact">Call Content Experts: +91-8708577598</Link>
                  </Button>
                </div>
              </section>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Sidebar currentService="content" currentLocation="kolkata" />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default ContentMarketingKolkata;
